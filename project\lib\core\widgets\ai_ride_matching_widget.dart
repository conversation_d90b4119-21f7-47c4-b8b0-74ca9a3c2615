import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/ai_ride_provider.dart';
import '../models/ride_request.dart';
import '../models/driver_model.dart';
import '../theme/app_colors.dart';

/// AI-powered ride matching widget with enhanced UI
class AIRideMatchingWidget extends StatefulWidget {
  final RideRequest rideRequest;
  final List<DriverModel> availableDrivers;
  final Function(Map<String, dynamic>) onDriverSelected;
  final VoidCallback? onRefresh;

  const AIRideMatchingWidget({
    Key? key,
    required this.rideRequest,
    required this.availableDrivers,
    required this.onDriverSelected,
    this.onRefresh,
  }) : super(key: key);

  @override
  State<AIRideMatchingWidget> createState() => _AIRideMatchingWidgetState();
}

class _AIRideMatchingWidgetState extends State<AIRideMatchingWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  bool _showAIInsights = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    
    _initializeAIMatching();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _initializeAIMatching() async {
    final aiProvider = Provider.of<AIRideProvider>(context, listen: false);
    await aiProvider.findAIMatches(
      rideRequest: widget.rideRequest,
      availableDrivers: widget.availableDrivers,
    );
    _animationController.forward();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AIRideProvider>(
      builder: (context, aiProvider, child) {
        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                AppColors.primary.withOpacity(0.05),
                AppColors.secondary.withOpacity(0.05),
              ],
            ),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: aiProvider.aiServiceConnected 
                ? AppColors.success.withOpacity(0.3)
                : AppColors.warning.withOpacity(0.3),
              width: 1,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHeader(aiProvider),
              if (aiProvider.isLoading) _buildLoadingState(),
              if (!aiProvider.isLoading && aiProvider.aiMatches.isNotEmpty)
                _buildMatchesList(aiProvider),
              if (!aiProvider.isLoading && aiProvider.aiMatches.isEmpty)
                _buildNoMatchesState(),
              if (aiProvider.error != null) _buildErrorState(aiProvider),
              if (_showAIInsights && aiProvider.aiServiceConnected)
                _buildAIInsights(aiProvider),
            ],
          ),
        );
      },
    );
  }

  Widget _buildHeader(AIRideProvider aiProvider) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: aiProvider.aiServiceConnected 
          ? AppColors.success.withOpacity(0.1)
          : AppColors.warning.withOpacity(0.1),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: aiProvider.aiServiceConnected 
                ? AppColors.success
                : AppColors.warning,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              aiProvider.aiServiceConnected 
                ? Icons.psychology 
                : Icons.psychology_outlined,
              color: Colors.white,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  aiProvider.aiServiceConnected 
                    ? 'AI-Powered Matching'
                    : 'Standard Matching',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  aiProvider.aiServiceConnected 
                    ? 'Smart algorithms finding optimal matches'
                    : 'AI service unavailable, using fallback',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () {
              setState(() {
                _showAIInsights = !_showAIInsights;
              });
            },
            icon: Icon(
              _showAIInsights ? Icons.expand_less : Icons.expand_more,
              color: AppColors.primary,
            ),
          ),
          IconButton(
            onPressed: widget.onRefresh ?? _initializeAIMatching,
            icon: const Icon(Icons.refresh, color: AppColors.primary),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingState() {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          const CircularProgressIndicator(color: AppColors.primary),
          const SizedBox(height: 16),
          Text(
            'AI is analyzing optimal matches...',
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMatchesList(AIRideProvider aiProvider) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: ListView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: aiProvider.aiMatches.length,
        itemBuilder: (context, index) {
          final match = aiProvider.aiMatches[index];
          return _buildMatchCard(match, index);
        },
      ),
    );
  }

  Widget _buildMatchCard(Map<String, dynamic> match, int index) {
    final isAIPowered = match['ai_powered'] ?? false;
    final matchScore = match['match_score'] ?? 0.0;
    final confidence = match['match_confidence'] ?? 0.0;
    final recommendation = match['ai_recommendation'] ?? '';
    
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isAIPowered 
            ? AppColors.primary.withOpacity(0.3)
            : Colors.grey.withOpacity(0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: () => widget.onDriverSelected(match),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  CircleAvatar(
                    radius: 24,
                    backgroundColor: AppColors.primary.withOpacity(0.1),
                    child: Text(
                      '${index + 1}',
                      style: const TextStyle(
                        color: AppColors.primary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Text(
                              'Driver ${match['driver_id']}',
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            if (isAIPowered) ...[
                              const SizedBox(width: 8),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 6,
                                  vertical: 2,
                                ),
                                decoration: BoxDecoration(
                                  color: AppColors.primary,
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: const Text(
                                  'AI',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 10,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ],
                          ],
                        ),
                        if (recommendation.isNotEmpty)
                          Text(
                            recommendation,
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                            ),
                          ),
                      ],
                    ),
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        '${match['pickup_eta'] ?? 5} min',
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: AppColors.primary,
                        ),
                      ),
                      Text(
                        '${(match['pickup_distance'] ?? 2.5).toStringAsFixed(1)} km',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              if (isAIPowered) ...[
                const SizedBox(height: 12),
                Row(
                  children: [
                    _buildScoreIndicator(
                      'Match Score',
                      matchScore,
                      AppColors.primary,
                    ),
                    const SizedBox(width: 16),
                    _buildScoreIndicator(
                      'Confidence',
                      confidence / 100,
                      AppColors.success,
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildScoreIndicator(String label, double value, Color color) {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 10,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 4),
          LinearProgressIndicator(
            value: value.clamp(0.0, 1.0),
            backgroundColor: color.withOpacity(0.2),
            valueColor: AlwaysStoppedAnimation<Color>(color),
          ),
          const SizedBox(height: 2),
          Text(
            '${(value * 100).toInt()}%',
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNoMatchesState() {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          Icon(
            Icons.search_off,
            size: 48,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'No matches found',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Try adjusting your preferences or wait for more drivers',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(AIRideProvider aiProvider) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.error.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: AppColors.error.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.error_outline,
            color: AppColors.error,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Matching Error',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: AppColors.error,
                  ),
                ),
                Text(
                  aiProvider.error ?? 'Unknown error occurred',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAIInsights(AIRideProvider aiProvider) {
    final serviceInfo = aiProvider.getAIServiceInfo();
    
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.primary.withOpacity(0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: AppColors.primary.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'AI Service Status',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: AppColors.primary,
            ),
          ),
          const SizedBox(height: 8),
          ...serviceInfo['features_available'].map<Widget>((feature) => 
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 2),
              child: Row(
                children: [
                  const Icon(
                    Icons.check_circle,
                    size: 16,
                    color: AppColors.success,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    feature,
                    style: const TextStyle(fontSize: 12),
                  ),
                ],
              ),
            ),
          ).toList(),
        ],
      ),
    );
  }
}
