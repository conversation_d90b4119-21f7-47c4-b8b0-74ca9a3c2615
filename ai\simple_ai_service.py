#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Simplified AI Service for Smart Ride-Sharing Application

This is a lightweight version of the AI service that provides core functionality
without heavy dependencies like transformers.

Features:
- Dynamic Pricing
- Ride Matching
- Predictive Maintenance (basic)
- Safety Monitoring (basic)

Author: Smart Ride-Sharing AI Team
"""

from flask import Flask, request, jsonify
from flask_cors import CORS
import logging
import json
from datetime import datetime
from typing import Dict, List, Any
import math
import random

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)
CORS(app)

# Service status
service_status = {
    'status': 'running',
    'version': '1.0.0-simplified',
    'features': [
        'dynamic_pricing',
        'ride_matching',
        'basic_maintenance',
        'basic_safety'
    ],
    'started_at': datetime.now().isoformat()
}

@app.route('/api/ai/status', methods=['GET'])
def get_service_status():
    """Get AI service status"""
    return jsonify(service_status)

# ============================================================================
# DYNAMIC PRICING ENDPOINTS
# ============================================================================

@app.route('/api/ai/pricing/calculate-price', methods=['POST'])
def calculate_dynamic_price():
    """Calculate dynamic price for a ride request"""
    try:
        data = request.get_json()
        
        if not data or 'ride_request' not in data:
            return jsonify({'error': 'Missing ride request data'}), 400
        
        ride_request = data['ride_request']
        market_data = data.get('market_data', {})
        
        # Basic pricing calculation
        distance_km = ride_request.get('distance_km', 5.0)
        duration_minutes = ride_request.get('duration_minutes', 15)
        vehicle_type = ride_request.get('vehicle_type', 'standard')
        time_of_day = ride_request.get('time_of_day', datetime.now().hour)
        
        # Base price calculation
        base_price_per_km = 2.0  # SAR per km
        base_price_per_minute = 0.5  # SAR per minute
        
        base_price = (distance_km * base_price_per_km) + (duration_minutes * base_price_per_minute)
        
        # Vehicle type multiplier
        vehicle_multipliers = {
            'standard': 1.0,
            'luxury': 1.5,
            'economy': 0.8
        }
        vehicle_multiplier = vehicle_multipliers.get(vehicle_type, 1.0)
        
        # Time-based surge pricing
        surge_multiplier = 1.0
        if 7 <= time_of_day <= 9 or 17 <= time_of_day <= 19:  # Peak hours
            surge_multiplier = 1.3
        elif 22 <= time_of_day or time_of_day <= 5:  # Night hours
            surge_multiplier = 1.2
        
        # Market conditions
        demand_level = market_data.get('demand_level', 'medium')
        if demand_level == 'high':
            surge_multiplier *= 1.2
        elif demand_level == 'low':
            surge_multiplier *= 0.9
        
        # Weather impact
        weather_condition = market_data.get('weather_condition', 'clear')
        if weather_condition in ['rain', 'storm']:
            surge_multiplier *= 1.15
        
        # Calculate final price
        final_price = base_price * vehicle_multiplier * surge_multiplier
        
        pricing_result = {
            'base_price': round(base_price, 2),
            'vehicle_multiplier': vehicle_multiplier,
            'surge_multiplier': round(surge_multiplier, 2),
            'final_price': round(final_price, 2),
            'currency': 'SAR',
            'breakdown': {
                'distance_cost': round(distance_km * base_price_per_km, 2),
                'time_cost': round(duration_minutes * base_price_per_minute, 2),
                'surge_amount': round(final_price - base_price, 2)
            }
        }
        
        return jsonify({
            'success': True,
            'pricing': pricing_result,
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"Error calculating dynamic price: {e}")
        return jsonify({'error': str(e)}), 500

# ============================================================================
# RIDE MATCHING ENDPOINTS
# ============================================================================

@app.route('/api/ai/ride-matching/find-matches', methods=['POST'])
def find_ride_matches():
    """Find optimal driver matches for a ride request"""
    try:
        data = request.get_json()
        
        if not data or 'ride_request' not in data:
            return jsonify({'error': 'Missing ride request data'}), 400
        
        ride_request = data['ride_request']
        available_drivers = data.get('available_drivers', [])
        
        pickup_lat = ride_request.get('pickup_lat', 0)
        pickup_lng = ride_request.get('pickup_lng', 0)
        
        matches = []
        
        for driver in available_drivers:
            driver_lat = driver.get('current_lat', 0)
            driver_lng = driver.get('current_lng', 0)
            
            # Calculate distance using Haversine formula
            distance = calculate_distance(pickup_lat, pickup_lng, driver_lat, driver_lng)
            
            # Skip if too far
            if distance > 10:  # 10km max
                continue
            
            # Calculate match score
            match_score = calculate_match_score(ride_request, driver, distance)
            
            # Estimate pickup time
            pickup_eta = max(2, int(distance * 2))  # 2 minutes per km, minimum 2 minutes
            
            match = {
                'driver_id': driver.get('id'),
                'driver': driver,
                'match_score': round(match_score, 2),
                'pickup_distance': round(distance, 2),
                'pickup_eta': pickup_eta,
                'estimated_cost': round(distance * 2.5, 2)  # Rough cost estimate
            }
            
            matches.append(match)
        
        # Sort by match score (highest first)
        matches.sort(key=lambda x: x['match_score'], reverse=True)
        
        return jsonify({
            'success': True,
            'matches': matches[:5],  # Return top 5 matches
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"Error finding ride matches: {e}")
        return jsonify({'error': str(e)}), 500

# ============================================================================
# INTEGRATED AI ENDPOINTS
# ============================================================================

@app.route('/api/ai/integrated/smart-ride-request', methods=['POST'])
def smart_ride_request():
    """Process a smart ride request using multiple AI systems"""
    try:
        data = request.get_json()
        
        if not data or 'ride_request' not in data:
            return jsonify({'error': 'Missing ride request data'}), 400
        
        ride_request = data['ride_request']
        market_data = data.get('market_data', {})
        available_drivers = data.get('available_drivers', [])
        
        # Calculate pricing
        pricing_data = {
            'ride_request': ride_request,
            'market_data': market_data
        }
        
        # Simulate pricing calculation
        distance_km = ride_request.get('distance_km', 5.0)
        base_price = distance_km * 2.0
        surge_multiplier = 1.2 if market_data.get('demand_level') == 'high' else 1.0
        final_price = base_price * surge_multiplier
        
        pricing_result = {
            'base_price': round(base_price, 2),
            'surge_multiplier': surge_multiplier,
            'final_price': round(final_price, 2),
            'currency': 'SAR'
        }
        
        # Find matches
        matches_data = {
            'ride_request': ride_request,
            'available_drivers': available_drivers
        }
        
        # Simulate match finding
        matches = []
        for i, driver in enumerate(available_drivers[:3]):  # Top 3 drivers
            match_score = 0.9 - (i * 0.1)  # Decreasing scores
            matches.append({
                'driver_id': driver.get('id'),
                'driver': driver,
                'match_score': match_score,
                'pickup_distance': 2.0 + i,
                'pickup_eta': 5 + (i * 2)
            })
        
        # Safety preparation
        safety_prep = {
            'monitoring_ready': True,
            'safety_features_enabled': True,
            'emergency_contacts_verified': True
        }
        
        return jsonify({
            'success': True,
            'pricing': pricing_result,
            'matches': matches,
            'safety_preparation': safety_prep,
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"Error processing smart ride request: {e}")
        return jsonify({'error': str(e)}), 500

# ============================================================================
# SAFETY MONITORING ENDPOINTS
# ============================================================================

@app.route('/api/ai/safety/start-monitoring', methods=['POST'])
def start_safety_monitoring():
    """Start safety monitoring for a ride"""
    try:
        data = request.get_json()
        
        if not data or 'ride_data' not in data:
            return jsonify({'error': 'Missing ride data'}), 400
        
        ride_data = data['ride_data']
        
        result = {
            'monitoring_started': True,
            'ride_id': ride_data.get('ride_id'),
            'safety_features': [
                'location_tracking',
                'speed_monitoring',
                'route_deviation_detection',
                'emergency_button'
            ],
            'check_in_interval': 300,  # 5 minutes
            'timestamp': datetime.now().isoformat()
        }
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"Error starting safety monitoring: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/ai/safety/update-location', methods=['POST'])
def update_ride_location():
    """Update ride location and perform safety checks"""
    try:
        data = request.get_json()
        
        if not data or 'ride_id' not in data or 'location_data' not in data:
            return jsonify({'error': 'Missing required data'}), 400
        
        ride_id = data['ride_id']
        location_data = data['location_data']
        
        # Basic safety checks
        current_speed = location_data.get('speed', 0)
        safety_alerts = []
        
        if current_speed > 120:  # Speed limit check
            safety_alerts.append({
                'type': 'speed_warning',
                'message': 'Vehicle exceeding safe speed limit',
                'severity': 'medium'
            })
        
        result = {
            'location_updated': True,
            'ride_id': ride_id,
            'safety_status': 'normal',
            'safety_alerts': safety_alerts,
            'timestamp': datetime.now().isoformat()
        }
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"Error updating ride location: {e}")
        return jsonify({'error': str(e)}), 500

# ============================================================================
# UTILITY FUNCTIONS
# ============================================================================

def calculate_distance(lat1, lng1, lat2, lng2):
    """Calculate distance between two points using Haversine formula"""
    R = 6371  # Earth's radius in kilometers
    
    lat1_rad = math.radians(lat1)
    lat2_rad = math.radians(lat2)
    delta_lat = math.radians(lat2 - lat1)
    delta_lng = math.radians(lng2 - lng1)
    
    a = (math.sin(delta_lat / 2) ** 2 +
         math.cos(lat1_rad) * math.cos(lat2_rad) * math.sin(delta_lng / 2) ** 2)
    c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))
    
    return R * c

def calculate_match_score(ride_request, driver, distance):
    """Calculate match score between rider and driver"""
    score = 1.0
    
    # Distance factor (closer is better)
    distance_score = max(0, 1 - (distance / 10))  # Normalize to 0-1
    score *= (0.4 * distance_score + 0.6)  # Weight distance 40%
    
    # Driver rating factor
    driver_rating = driver.get('rating', 4.0)
    rating_score = driver_rating / 5.0
    score *= (0.3 * rating_score + 0.7)  # Weight rating 30%
    
    # Vehicle type compatibility
    preferred_type = ride_request.get('vehicle_type_preference', 'standard')
    driver_type = driver.get('vehicle_type', 'standard')
    
    if preferred_type == driver_type:
        score *= 1.1  # 10% bonus for exact match
    elif preferred_type == 'luxury' and driver_type == 'standard':
        score *= 0.8  # Penalty for downgrade
    
    # Random factor for variety
    score *= (0.9 + random.random() * 0.2)  # ±10% random variation
    
    return min(1.0, score)

if __name__ == '__main__':
    # Start the simplified AI service
    logger.info("Starting Simplified AI Service for Smart Ride-Sharing...")
    logger.info(f"Service features: {', '.join(service_status['features'])}")
    
    app.run(
        host='0.0.0.0',
        port=5001,
        debug=True
    )
