# C/C++ build system timings
generate_cxx_metadata
  [gap of 210ms]
  create-invalidation-state 326ms
  [gap of 122ms]
  write-metadata-json-to-file 49ms
generate_cxx_metadata completed in 708ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 130ms]
  create-invalidation-state 173ms
  [gap of 58ms]
  write-metadata-json-to-file 33ms
generate_cxx_metadata completed in 395ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 155ms]
  create-invalidation-state 210ms
  [gap of 82ms]
  write-metadata-json-to-file 54ms
generate_cxx_metadata completed in 503ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 68ms]
  create-invalidation-state 149ms
  [gap of 47ms]
  write-metadata-json-to-file 39ms
generate_cxx_metadata completed in 304ms

