import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/ai_ride_provider.dart';
import '../theme/app_colors.dart';

/// AI Status Indicator Widget
/// Shows the current status of AI services in the app
class AIStatusIndicator extends StatefulWidget {
  final bool showDetails;
  final VoidCallback? onTap;

  const AIStatusIndicator({
    Key? key,
    this.showDetails = false,
    this.onTap,
  }) : super(key: key);

  @override
  State<AIStatusIndicator> createState() => _AIStatusIndicatorState();
}

class _AIStatusIndicatorState extends State<AIStatusIndicator>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AIRideProvider>(
      builder: (context, aiProvider, child) {
        return GestureDetector(
          onTap: widget.onTap ?? () => _showAIStatusDialog(context, aiProvider),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: _getStatusColor(aiProvider.aiServiceConnected).withOpacity(0.1),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: _getStatusColor(aiProvider.aiServiceConnected),
                width: 1,
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                AnimatedBuilder(
                  animation: _pulseAnimation,
                  builder: (context, child) {
                    return Transform.scale(
                      scale: aiProvider.aiServiceConnected ? _pulseAnimation.value : 1.0,
                      child: Icon(
                        aiProvider.aiServiceConnected 
                          ? Icons.psychology 
                          : Icons.psychology_outlined,
                        size: 16,
                        color: _getStatusColor(aiProvider.aiServiceConnected),
                      ),
                    );
                  },
                ),
                const SizedBox(width: 6),
                Text(
                  aiProvider.aiServiceConnected ? 'AI ON' : 'AI OFF',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: _getStatusColor(aiProvider.aiServiceConnected),
                  ),
                ),
                if (widget.showDetails) ...[
                  const SizedBox(width: 4),
                  Icon(
                    Icons.info_outline,
                    size: 12,
                    color: _getStatusColor(aiProvider.aiServiceConnected),
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  Color _getStatusColor(bool connected) {
    return connected ? AppColors.success : AppColors.warning;
  }

  void _showAIStatusDialog(BuildContext context, AIRideProvider aiProvider) {
    final serviceInfo = aiProvider.getAIServiceInfo();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              Icons.psychology,
              color: AppColors.primary,
            ),
            const SizedBox(width: 8),
            const Text('AI Service Status'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildStatusRow(
              'Connection',
              serviceInfo['connected'] ? 'Connected' : 'Disconnected',
              serviceInfo['connected'],
            ),
            const SizedBox(height: 12),
            const Text(
              'Available Features:',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 14,
              ),
            ),
            const SizedBox(height: 8),
            if (serviceInfo['features_available'].isNotEmpty) ...[
              ...serviceInfo['features_available'].map<Widget>((feature) => 
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 2),
                  child: Row(
                    children: [
                      const Icon(
                        Icons.check_circle,
                        size: 16,
                        color: AppColors.success,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          feature,
                          style: const TextStyle(fontSize: 12),
                        ),
                      ),
                    ],
                  ),
                ),
              ).toList(),
            ] else ...[
              Row(
                children: [
                  const Icon(
                    Icons.warning,
                    size: 16,
                    color: AppColors.warning,
                  ),
                  const SizedBox(width: 8),
                  const Text(
                    'No AI features available',
                    style: TextStyle(fontSize: 12),
                  ),
                ],
              ),
            ],
            if (serviceInfo['last_error'] != null) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.error.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Row(
                  children: [
                    const Icon(
                      Icons.error_outline,
                      size: 16,
                      color: AppColors.error,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        serviceInfo['last_error'],
                        style: const TextStyle(
                          fontSize: 11,
                          color: AppColors.error,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              await aiProvider.initializeAIService();
            },
            child: const Text('Retry Connection'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusRow(String label, String value, bool isPositive) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          '$label:',
          style: const TextStyle(fontWeight: FontWeight.w500),
        ),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
          decoration: BoxDecoration(
            color: (isPositive ? AppColors.success : AppColors.error).withOpacity(0.1),
            borderRadius: BorderRadius.circular(4),
          ),
          child: Text(
            value,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: isPositive ? AppColors.success : AppColors.error,
            ),
          ),
        ),
      ],
    );
  }
}

/// Compact AI Status Badge for use in app bars
class AIStatusBadge extends StatelessWidget {
  const AIStatusBadge({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer<AIRideProvider>(
      builder: (context, aiProvider, child) {
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
          decoration: BoxDecoration(
            color: aiProvider.aiServiceConnected 
              ? AppColors.success.withOpacity(0.2)
              : AppColors.warning.withOpacity(0.2),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                aiProvider.aiServiceConnected 
                  ? Icons.psychology 
                  : Icons.psychology_outlined,
                size: 12,
                color: aiProvider.aiServiceConnected 
                  ? AppColors.success 
                  : AppColors.warning,
              ),
              const SizedBox(width: 4),
              Text(
                'AI',
                style: TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                  color: aiProvider.aiServiceConnected 
                    ? AppColors.success 
                    : AppColors.warning,
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
