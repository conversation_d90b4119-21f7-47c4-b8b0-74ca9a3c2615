#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script for the simplified AI service

This script tests the basic functionality of the AI service to ensure
it's working correctly for the Flutter app integration.
"""

import requests
import json
import time

# AI Service URL
AI_SERVICE_URL = "http://localhost:5001/api/ai"

def test_service_status():
    """Test if the AI service is running"""
    print("🔍 Testing AI Service Status...")
    try:
        response = requests.get(f"{AI_SERVICE_URL}/status", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Service Status: {data['status']}")
            print(f"   ✅ Version: {data['version']}")
            print(f"   ✅ Features: {', '.join(data['features'])}")
            return True
        else:
            print(f"   ❌ Status check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def test_dynamic_pricing():
    """Test dynamic pricing calculation"""
    print("\n💰 Testing Dynamic Pricing...")
    try:
        test_data = {
            "ride_request": {
                "distance_km": 8.5,
                "duration_minutes": 20,
                "pickup_lat": 24.7136,
                "pickup_lng": 46.6753,
                "dropoff_lat": 24.7453,
                "dropoff_lng": 46.6890,
                "vehicle_type": "standard",
                "time_of_day": 18  # Peak hour
            },
            "market_data": {
                "demand_level": "high",
                "supply_level": "medium",
                "weather_condition": "clear",
                "is_peak_hour": True,
                "special_events": False
            }
        }
        
        response = requests.post(
            f"{AI_SERVICE_URL}/pricing/calculate-price",
            json=test_data,
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            if data['success']:
                pricing = data['pricing']
                print(f"   ✅ Base Price: {pricing['base_price']} SAR")
                print(f"   ✅ Final Price: {pricing['final_price']} SAR")
                print(f"   ✅ Surge Multiplier: {pricing['surge_multiplier']}x")
                return True
            else:
                print("   ❌ Pricing calculation failed")
                return False
        else:
            print(f"   ❌ Request failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def test_ride_matching():
    """Test ride matching functionality"""
    print("\n🚗 Testing Ride Matching...")
    try:
        test_data = {
            "ride_request": {
                "pickup_lat": 24.7136,
                "pickup_lng": 46.6753,
                "dropoff_lat": 24.7453,
                "dropoff_lng": 46.6890,
                "passenger_count": 1,
                "vehicle_type_preference": "standard"
            },
            "available_drivers": [
                {
                    "id": "driver_001",
                    "current_lat": 24.7100,
                    "current_lng": 46.6700,
                    "rating": 4.5,
                    "vehicle_type": "standard",
                    "available_seats": 4,
                    "vehicle_year": 2020
                },
                {
                    "id": "driver_002",
                    "current_lat": 24.7200,
                    "current_lng": 46.6800,
                    "rating": 4.8,
                    "vehicle_type": "luxury",
                    "available_seats": 3,
                    "vehicle_year": 2022
                },
                {
                    "id": "driver_003",
                    "current_lat": 24.7050,
                    "current_lng": 46.6750,
                    "rating": 4.2,
                    "vehicle_type": "standard",
                    "available_seats": 4,
                    "vehicle_year": 2019
                }
            ]
        }
        
        response = requests.post(
            f"{AI_SERVICE_URL}/ride-matching/find-matches",
            json=test_data,
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            if data['success']:
                matches = data['matches']
                print(f"   ✅ Found {len(matches)} driver matches")
                for i, match in enumerate(matches[:3]):  # Show top 3
                    print(f"   ✅ Match {i+1}: Driver {match['driver_id']} "
                          f"(Score: {match['match_score']:.2f}, "
                          f"Distance: {match['pickup_distance']:.1f}km, "
                          f"ETA: {match['pickup_eta']}min)")
                return True
            else:
                print("   ❌ Ride matching failed")
                return False
        else:
            print(f"   ❌ Request failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def test_smart_ride_request():
    """Test integrated smart ride request"""
    print("\n🚀 Testing Smart Ride Request...")
    try:
        test_data = {
            "ride_request": {
                "pickup_lat": 24.7136,
                "pickup_lng": 46.6753,
                "dropoff_lat": 24.7453,
                "dropoff_lng": 46.6890,
                "distance_km": 8.5,
                "duration_minutes": 20,
                "passenger_count": 1,
                "vehicle_type_preference": "standard"
            },
            "market_data": {
                "demand_level": "medium",
                "supply_level": "high",
                "weather_condition": "clear",
                "is_peak_hour": False
            },
            "available_drivers": [
                {
                    "id": "driver_001",
                    "current_lat": 24.7100,
                    "current_lng": 46.6700,
                    "rating": 4.5,
                    "vehicle_type": "standard"
                }
            ]
        }
        
        response = requests.post(
            f"{AI_SERVICE_URL}/integrated/smart-ride-request",
            json=test_data,
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            if data['success']:
                print(f"   ✅ Smart processing completed")
                print(f"   ✅ Pricing: {data['pricing']['final_price']} SAR")
                print(f"   ✅ Matches: {len(data['matches'])} drivers")
                print(f"   ✅ Safety: {'Ready' if data['safety_preparation']['monitoring_ready'] else 'Not Ready'}")
                return True
            else:
                print("   ❌ Smart ride request failed")
                return False
        else:
            print(f"   ❌ Request failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def test_safety_monitoring():
    """Test safety monitoring functionality"""
    print("\n🛡️ Testing Safety Monitoring...")
    try:
        # Start monitoring
        test_data = {
            "ride_data": {
                "ride_id": "RIDE_TEST_001",
                "driver_id": "DRIVER_001",
                "rider_id": "RIDER_001",
                "pickup_location": [24.7136, 46.6753],
                "dropoff_location": [24.7453, 46.6890],
                "estimated_duration": 20
            }
        }
        
        response = requests.post(
            f"{AI_SERVICE_URL}/safety/start-monitoring",
            json=test_data,
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            if data['monitoring_started']:
                print(f"   ✅ Safety monitoring started for ride {data['ride_id']}")
                print(f"   ✅ Features: {', '.join(data['safety_features'])}")
                
                # Test location update
                location_data = {
                    "ride_id": "RIDE_TEST_001",
                    "location_data": {
                        "current_location": [24.7150, 46.6760],
                        "speed": 45,
                        "timestamp": "2024-01-01T12:00:00Z"
                    }
                }
                
                response2 = requests.post(
                    f"{AI_SERVICE_URL}/safety/update-location",
                    json=location_data,
                    timeout=10
                )
                
                if response2.status_code == 200:
                    data2 = response2.json()
                    print(f"   ✅ Location updated: {data2['safety_status']}")
                    return True
                else:
                    print("   ❌ Location update failed")
                    return False
            else:
                print("   ❌ Safety monitoring failed to start")
                return False
        else:
            print(f"   ❌ Request failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def main():
    """Run all tests"""
    print("🤖 AI Service Integration Test")
    print("=" * 50)
    
    # Wait a moment for service to be ready
    print("⏳ Waiting for AI service to be ready...")
    time.sleep(2)
    
    tests = [
        ("Service Status", test_service_status),
        ("Dynamic Pricing", test_dynamic_pricing),
        ("Ride Matching", test_ride_matching),
        ("Smart Ride Request", test_smart_ride_request),
        ("Safety Monitoring", test_safety_monitoring)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"   ❌ Test failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n📊 Test Results Summary:")
    print("-" * 40)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:20} {status}")
        if result:
            passed += 1
    
    print("-" * 40)
    print(f"Total: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 All AI features are working correctly!")
        print("🚀 Ready for Flutter integration!")
        return True
    elif passed > total // 2:
        print(f"\n⚠️  Most AI features are working ({passed}/{total} passed)")
        print("🔧 Minor issues need attention")
        return True
    else:
        print(f"\n❌ Multiple AI features need attention ({passed}/{total} passed)")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
