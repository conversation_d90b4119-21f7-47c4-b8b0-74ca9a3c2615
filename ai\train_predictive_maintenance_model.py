#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Predictive Maintenance Model Trainer

This script generates comprehensive synthetic data and trains machine learning models
for the predictive maintenance system. It creates realistic vehicle telemetry data
and saves optimized models to the models/ directory.

Models trained:
1. Component health prediction model - Predicts component health scores
2. Failure prediction model - Predicts component failure probability
3. Maintenance scheduling model - Optimizes maintenance schedules
4. Anomaly detection model - Detects unusual patterns in telemetry
5. Cost optimization model - Optimizes maintenance costs

Features:
- Realistic vehicle telemetry simulation
- Component degradation modeling
- Failure pattern analysis
- Maintenance cost optimization
- Anomaly detection algorithms

Author: Smart Ride-Sharing AI Team
"""

import numpy as np
import pandas as pd
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.ensemble import RandomForestClassifier, IsolationForest
from sklearn.model_selection import train_test_split, GridSearchCV
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.metrics import classification_report, accuracy_score, roc_auc_score
import joblib
import logging
import os
from datetime import datetime, timedelta
import random
from typing import Dict, List, Tuple
import warnings
warnings.filterwarnings('ignore')

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PredictiveMaintenanceModelTrainer:
    """Comprehensive trainer for predictive maintenance models"""

    def __init__(self, models_dir: str = "models"):
        self.models_dir = models_dir
        os.makedirs(models_dir, exist_ok=True)

        # Initialize scalers and encoders
        self.health_scaler = StandardScaler()
        self.failure_scaler = StandardScaler()
        self.anomaly_scaler = StandardScaler()
        self.label_encoders = {}

        # Model parameters
        self.random_state = 42
        self.test_size = 0.2
        self.cv_folds = 5

        # Vehicle components and their characteristics
        self.components = {
            'engine': {'base_life': 200000, 'degradation_rate': 0.001, 'failure_threshold': 20},
            'transmission': {'base_life': 150000, 'degradation_rate': 0.0012, 'failure_threshold': 25},
            'brakes': {'base_life': 50000, 'degradation_rate': 0.002, 'failure_threshold': 15},
            'tires': {'base_life': 40000, 'degradation_rate': 0.0025, 'failure_threshold': 10},
            'battery': {'base_life': 60000, 'degradation_rate': 0.0015, 'failure_threshold': 20},
            'suspension': {'base_life': 80000, 'degradation_rate': 0.0018, 'failure_threshold': 30},
            'air_conditioning': {'base_life': 100000, 'degradation_rate': 0.001, 'failure_threshold': 25},
            'electrical': {'base_life': 120000, 'degradation_rate': 0.0008, 'failure_threshold': 35}
        }

        logger.info("Predictive Maintenance Model Trainer initialized")

    def generate_telemetry_data(self, n_samples: int = 50000) -> pd.DataFrame:
        """Generate realistic vehicle telemetry training data"""
        logger.info(f"Generating {n_samples} telemetry samples...")

        data = []

        for _ in range(n_samples):
            # Vehicle characteristics
            vehicle_id = f"VEH_{random.randint(1000, 9999)}"
            vehicle_age_months = random.randint(1, 120)  # 1-10 years
            mileage = random.randint(5000, 300000)  # km
            vehicle_type = random.choice(['sedan', 'suv', 'hatchback', 'luxury'])

            # Usage patterns
            daily_distance = random.uniform(50, 300)  # km per day
            trips_per_day = random.randint(5, 25)
            avg_speed = random.uniform(25, 80)  # km/h
            city_driving_ratio = random.uniform(0.3, 0.9)

            # Environmental factors
            temperature = random.uniform(-5, 50)  # Celsius
            humidity = random.uniform(20, 90)  # %
            road_quality = random.choice(['excellent', 'good', 'fair', 'poor'])

            # Generate component data
            component_data = {}
            for component, specs in self.components.items():
                health_score, telemetry_values = self._generate_component_telemetry(
                    component, specs, mileage, vehicle_age_months,
                    daily_distance, temperature, road_quality
                )

                component_data[f'{component}_health'] = health_score
                component_data.update({f'{component}_{k}': v for k, v in telemetry_values.items()})

            # Overall vehicle health
            overall_health = np.mean([component_data[f'{comp}_health'] for comp in self.components.keys()])

            # Maintenance history
            last_service_km = mileage - random.randint(0, 15000)
            services_count = max(1, mileage // 10000)

            # Create sample
            sample = {
                'vehicle_id': vehicle_id,
                'vehicle_age_months': vehicle_age_months,
                'mileage': mileage,
                'vehicle_type': vehicle_type,
                'daily_distance': daily_distance,
                'trips_per_day': trips_per_day,
                'avg_speed': avg_speed,
                'city_driving_ratio': city_driving_ratio,
                'temperature': temperature,
                'humidity': humidity,
                'road_quality': road_quality,
                'last_service_km': last_service_km,
                'services_count': services_count,
                'overall_health': overall_health,
                **component_data
            }

            data.append(sample)

        df = pd.DataFrame(data)
        logger.info(f"Generated telemetry data with shape: {df.shape}")
        return df

    def _generate_component_telemetry(self, component: str, specs: Dict,
                                    mileage: int, age_months: int,
                                    daily_distance: float, temperature: float,
                                    road_quality: str) -> Tuple[float, Dict]:
        """Generate realistic telemetry for a specific component"""

        # Calculate wear based on usage
        usage_factor = mileage / specs['base_life']
        age_factor = age_months / 60  # Normalize to 5 years

        # Environmental impact
        temp_impact = abs(temperature - 25) / 25  # Optimal temp is 25°C
        road_impact = {'excellent': 0, 'good': 0.1, 'fair': 0.2, 'poor': 0.4}[road_quality]

        # Calculate health score (0-100)
        degradation = (usage_factor + age_factor * 0.5 + temp_impact * 0.2 + road_impact * 0.3)
        health_score = max(0, 100 - degradation * 100)

        # Add component-specific noise
        health_score += random.gauss(0, 5)
        health_score = max(0, min(100, health_score))

        # Generate component-specific telemetry values
        telemetry_values = self._get_component_specific_telemetry(
            component, health_score, temperature, daily_distance
        )

        return health_score, telemetry_values

    def _get_component_specific_telemetry(self, component: str, health_score: float,
                                        temperature: float, daily_distance: float) -> Dict:
        """Generate component-specific telemetry values"""

        # Base values that degrade with health
        degradation_factor = (100 - health_score) / 100

        if component == 'engine':
            return {
                'rpm_avg': 2000 + degradation_factor * 500 + random.gauss(0, 100),
                'oil_pressure': max(20, 40 - degradation_factor * 15 + random.gauss(0, 3)),
                'coolant_temp': 90 + degradation_factor * 20 + random.gauss(0, 5),
                'fuel_efficiency': max(5, 12 - degradation_factor * 3 + random.gauss(0, 1))
            }
        elif component == 'transmission':
            return {
                'shift_quality': max(1, 10 - degradation_factor * 5 + random.gauss(0, 1)),
                'fluid_temp': 80 + degradation_factor * 30 + random.gauss(0, 5),
                'gear_ratio_efficiency': max(0.7, 0.95 - degradation_factor * 0.2 + random.gauss(0, 0.05))
            }
        elif component == 'brakes':
            return {
                'pad_thickness': max(2, 12 - degradation_factor * 8 + random.gauss(0, 1)),
                'brake_pressure': max(800, 1200 - degradation_factor * 300 + random.gauss(0, 50)),
                'stopping_distance': 35 + degradation_factor * 15 + random.gauss(0, 3)
            }
        elif component == 'tires':
            return {
                'tread_depth': max(1, 8 - degradation_factor * 6 + random.gauss(0, 0.5)),
                'pressure': max(25, 32 - degradation_factor * 5 + random.gauss(0, 2)),
                'wear_pattern': random.choice(['even', 'uneven']) if degradation_factor > 0.3 else 'even'
            }
        elif component == 'battery':
            return {
                'voltage': max(11, 12.6 - degradation_factor * 1.5 + random.gauss(0, 0.2)),
                'capacity': max(30, 100 - degradation_factor * 60 + random.gauss(0, 5)),
                'charge_cycles': int(degradation_factor * 1000 + random.gauss(0, 100))
            }
        elif component == 'suspension':
            return {
                'damping_efficiency': max(0.4, 0.9 - degradation_factor * 0.4 + random.gauss(0, 0.05)),
                'spring_compression': 50 + degradation_factor * 30 + random.gauss(0, 5),
                'ride_comfort': max(1, 10 - degradation_factor * 6 + random.gauss(0, 1))
            }
        elif component == 'air_conditioning':
            return {
                'cooling_efficiency': max(0.3, 0.9 - degradation_factor * 0.5 + random.gauss(0, 0.05)),
                'refrigerant_pressure': max(150, 250 - degradation_factor * 80 + random.gauss(0, 10)),
                'power_consumption': 2000 + degradation_factor * 800 + random.gauss(0, 100)
            }
        elif component == 'electrical':
            return {
                'alternator_output': max(12, 14.4 - degradation_factor * 2 + random.gauss(0, 0.3)),
                'wire_resistance': 0.1 + degradation_factor * 0.5 + random.gauss(0, 0.05),
                'system_efficiency': max(0.6, 0.95 - degradation_factor * 0.3 + random.gauss(0, 0.05))
            }

        return {}

    def generate_failure_data(self, n_samples: int = 30000) -> pd.DataFrame:
        """Generate failure prediction training data"""
        logger.info(f"Generating {n_samples} failure prediction samples...")

        data = []

        for _ in range(n_samples):
            # Component selection
            component = random.choice(list(self.components.keys()))
            specs = self.components[component]

            # Vehicle and usage data
            mileage = random.randint(10000, 250000)
            age_months = random.randint(6, 100)
            daily_usage = random.uniform(50, 400)  # km per day

            # Current health score
            health_score = random.uniform(10, 95)

            # Environmental factors
            avg_temperature = random.uniform(15, 45)
            road_conditions = random.choice(['excellent', 'good', 'fair', 'poor'])
            maintenance_quality = random.choice(['excellent', 'good', 'fair', 'poor'])

            # Calculate failure probability
            failure_prob = self._calculate_failure_probability(
                component, health_score, mileage, age_months,
                daily_usage, road_conditions, maintenance_quality
            )

            # Determine if failure occurs (binary classification)
            will_fail = 1 if failure_prob > 0.7 else 0

            # Time to failure (in days)
            if will_fail:
                time_to_failure = max(1, int(random.exponential(30)))  # Exponential distribution
            else:
                time_to_failure = random.randint(90, 365)  # Longer time if no failure

            data.append({
                'component': component,
                'health_score': health_score,
                'mileage': mileage,
                'age_months': age_months,
                'daily_usage': daily_usage,
                'avg_temperature': avg_temperature,
                'road_conditions': road_conditions,
                'maintenance_quality': maintenance_quality,
                'failure_probability': failure_prob,
                'will_fail': will_fail,
                'time_to_failure': time_to_failure
            })

        df = pd.DataFrame(data)
        logger.info(f"Generated failure data with shape: {df.shape}")
        return df

    def _calculate_failure_probability(self, component: str, health_score: float,
                                     mileage: int, age_months: int, daily_usage: float,
                                     road_conditions: str, maintenance_quality: str) -> float:
        """Calculate realistic failure probability"""

        # Base probability from health score
        base_prob = max(0, (100 - health_score) / 100)

        # Age factor
        age_factor = min(1.0, age_months / 120)  # Normalize to 10 years

        # Usage factor
        usage_factor = min(1.0, daily_usage / 300)  # Normalize to 300km/day

        # Environmental factors
        road_factor = {'excellent': 0, 'good': 0.1, 'fair': 0.3, 'poor': 0.5}[road_conditions]
        maintenance_factor = {'excellent': -0.2, 'good': 0, 'fair': 0.2, 'poor': 0.4}[maintenance_quality]

        # Component-specific adjustments
        component_factors = {
            'brakes': 1.2,  # Higher failure rate
            'tires': 1.1,
            'battery': 1.0,
            'engine': 0.8,  # Lower failure rate
            'transmission': 0.9,
            'suspension': 0.9,
            'air_conditioning': 1.0,
            'electrical': 1.1
        }

        # Calculate final probability
        failure_prob = (base_prob * 0.6 +
                       age_factor * 0.2 +
                       usage_factor * 0.1 +
                       road_factor * 0.05 +
                       maintenance_factor * 0.05) * component_factors.get(component, 1.0)

        return max(0, min(1, failure_prob))

    def generate_maintenance_schedule_data(self, n_samples: int = 20000) -> pd.DataFrame:
        """Generate maintenance scheduling optimization data"""
        logger.info(f"Generating {n_samples} maintenance scheduling samples...")

        data = []

        for _ in range(n_samples):
            # Vehicle fleet data
            fleet_size = random.randint(10, 100)
            available_mechanics = random.randint(2, 15)
            service_bay_capacity = random.randint(3, 20)

            # Maintenance request
            urgency_level = random.choice(['low', 'medium', 'high', 'critical'])
            estimated_duration = random.uniform(1, 8)  # hours
            required_parts_cost = random.uniform(50, 2000)  # SAR

            # Scheduling constraints
            current_workload = random.uniform(0.3, 1.0)  # Utilization
            next_available_slot = random.randint(0, 72)  # hours

            # Vehicle importance
            vehicle_utilization = random.uniform(0.4, 0.95)
            revenue_per_hour = random.uniform(50, 200)  # SAR

            # Calculate optimal scheduling score
            scheduling_score = self._calculate_scheduling_score(
                urgency_level, estimated_duration, current_workload,
                vehicle_utilization, revenue_per_hour, next_available_slot
            )

            # Calculate cost impact
            delay_cost = self._calculate_delay_cost(
                urgency_level, vehicle_utilization, revenue_per_hour, next_available_slot
            )

            data.append({
                'fleet_size': fleet_size,
                'available_mechanics': available_mechanics,
                'service_bay_capacity': service_bay_capacity,
                'urgency_level': urgency_level,
                'estimated_duration': estimated_duration,
                'required_parts_cost': required_parts_cost,
                'current_workload': current_workload,
                'next_available_slot': next_available_slot,
                'vehicle_utilization': vehicle_utilization,
                'revenue_per_hour': revenue_per_hour,
                'scheduling_score': scheduling_score,
                'delay_cost': delay_cost
            })

        df = pd.DataFrame(data)
        logger.info(f"Generated maintenance scheduling data with shape: {df.shape}")
        return df

    def _calculate_scheduling_score(self, urgency: str, duration: float,
                                  workload: float, utilization: float,
                                  revenue_per_hour: float, next_slot: int) -> float:
        """Calculate optimal scheduling score (0-100)"""

        # Urgency weights
        urgency_weights = {'low': 0.2, 'medium': 0.5, 'high': 0.8, 'critical': 1.0}
        urgency_score = urgency_weights[urgency]

        # Utilization impact (higher utilization = higher priority)
        utilization_score = utilization

        # Revenue impact
        revenue_score = min(1.0, revenue_per_hour / 200)

        # Availability impact (sooner is better)
        availability_score = max(0, 1.0 - next_slot / 72)

        # Workload impact (lower workload = better scheduling)
        workload_score = 1.0 - workload

        # Weighted final score
        final_score = (urgency_score * 0.4 +
                      utilization_score * 0.25 +
                      revenue_score * 0.15 +
                      availability_score * 0.1 +
                      workload_score * 0.1) * 100

        return round(final_score, 2)

    def _calculate_delay_cost(self, urgency: str, utilization: float,
                            revenue_per_hour: float, delay_hours: int) -> float:
        """Calculate cost of delaying maintenance"""

        # Base delay cost
        base_cost = delay_hours * revenue_per_hour * utilization

        # Urgency multiplier
        urgency_multipliers = {'low': 0.5, 'medium': 1.0, 'high': 2.0, 'critical': 5.0}
        urgency_multiplier = urgency_multipliers[urgency]

        # Calculate total delay cost
        total_cost = base_cost * urgency_multiplier

        return round(total_cost, 2)

    def prepare_features(self, df: pd.DataFrame, target_col: str) -> Tuple[np.ndarray, np.ndarray]:
        """Prepare features for model training"""

        # Encode categorical variables
        categorical_cols = df.select_dtypes(include=['object']).columns
        df_encoded = df.copy()

        for col in categorical_cols:
            if col != target_col and col in df.columns:
                if col not in self.label_encoders:
                    self.label_encoders[col] = LabelEncoder()
                    df_encoded[col] = self.label_encoders[col].fit_transform(df[col])
                else:
                    df_encoded[col] = self.label_encoders[col].transform(df[col])

        # Separate features and target
        X = df_encoded.drop(columns=[target_col])
        y = df_encoded[target_col]

        return X.values, y.values

    def train_health_prediction_model(self, telemetry_data: pd.DataFrame) -> None:
        """Train component health prediction model"""
        logger.info("Training component health prediction model...")

        # Prepare data for each component
        for component in self.components.keys():
            health_col = f'{component}_health'
            if health_col in telemetry_data.columns:
                # Select relevant features for this component
                feature_cols = [col for col in telemetry_data.columns
                               if col.startswith(component) and col != health_col]
                feature_cols.extend(['mileage', 'vehicle_age_months', 'daily_distance',
                                   'temperature', 'road_quality'])

                # Prepare features
                component_data = telemetry_data[feature_cols + [health_col]].copy()
                X, y = self.prepare_features(component_data, health_col)
                X_scaled = self.health_scaler.fit_transform(X)

                # Split data
                X_train, X_test, y_train, y_test = train_test_split(
                    X_scaled, y, test_size=self.test_size, random_state=self.random_state
                )

                # Train model
                model = RandomForestRegressor(
                    n_estimators=200, max_depth=15, random_state=self.random_state
                )
                model.fit(X_train, y_train)

                # Evaluate
                y_pred = model.predict(X_test)
                mse = mean_squared_error(y_test, y_pred)
                mae = mean_absolute_error(y_test, y_pred)
                r2 = r2_score(y_test, y_pred)

                logger.info(f"{component} Health Model - MSE: {mse:.4f}, MAE: {mae:.4f}, R²: {r2:.4f}")

                # Save model
                joblib.dump(model, f"{self.models_dir}/{component}_health_model.pkl")

        # Save scaler
        joblib.dump(self.health_scaler, f"{self.models_dir}/health_scaler.pkl")
        logger.info("Component health prediction models trained and saved")

    def train_failure_prediction_model(self, failure_data: pd.DataFrame) -> None:
        """Train failure prediction model"""
        logger.info("Training failure prediction model...")

        # Prepare features
        X, y = self.prepare_features(failure_data, 'will_fail')
        X_scaled = self.failure_scaler.fit_transform(X)

        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            X_scaled, y, test_size=self.test_size, random_state=self.random_state
        )

        # Train classification model
        model = RandomForestClassifier(
            n_estimators=200, max_depth=15, random_state=self.random_state
        )
        model.fit(X_train, y_train)

        # Evaluate
        y_pred = model.predict(X_test)
        accuracy = accuracy_score(y_test, y_pred)
        auc_score = roc_auc_score(y_test, model.predict_proba(X_test)[:, 1])

        logger.info(f"Failure Prediction Model - Accuracy: {accuracy:.4f}, AUC: {auc_score:.4f}")

        # Save model and scaler
        joblib.dump(model, f"{self.models_dir}/failure_prediction_model.pkl")
        joblib.dump(self.failure_scaler, f"{self.models_dir}/failure_scaler.pkl")

        logger.info("Failure prediction model trained and saved")

    def train_anomaly_detection_model(self, telemetry_data: pd.DataFrame) -> None:
        """Train anomaly detection model"""
        logger.info("Training anomaly detection model...")

        # Select numerical features for anomaly detection
        numerical_cols = telemetry_data.select_dtypes(include=[np.number]).columns
        X = telemetry_data[numerical_cols].values
        X_scaled = self.anomaly_scaler.fit_transform(X)

        # Train Isolation Forest for anomaly detection
        model = IsolationForest(
            contamination=0.1, random_state=self.random_state, n_estimators=100
        )
        model.fit(X_scaled)

        # Evaluate on training data
        anomaly_scores = model.decision_function(X_scaled)
        anomalies = model.predict(X_scaled)
        anomaly_rate = (anomalies == -1).mean()

        logger.info(f"Anomaly Detection Model - Anomaly Rate: {anomaly_rate:.4f}")

        # Save model and scaler
        joblib.dump(model, f"{self.models_dir}/anomaly_detection_model.pkl")
        joblib.dump(self.anomaly_scaler, f"{self.models_dir}/anomaly_scaler.pkl")

        logger.info("Anomaly detection model trained and saved")

    def train_all_models(self) -> None:
        """Generate data and train all predictive maintenance models"""
        logger.info("Starting comprehensive predictive maintenance model training...")

        # Generate training data
        logger.info("Generating training datasets...")
        telemetry_data = self.generate_telemetry_data(50000)
        failure_data = self.generate_failure_data(30000)
        schedule_data = self.generate_maintenance_schedule_data(20000)

        # Train models
        self.train_health_prediction_model(telemetry_data)
        self.train_failure_prediction_model(failure_data)
        self.train_anomaly_detection_model(telemetry_data)

        # Save label encoders
        joblib.dump(self.label_encoders, f"{self.models_dir}/maintenance_label_encoders.pkl")

        logger.info("All predictive maintenance models trained successfully!")
        logger.info(f"Models saved to: {self.models_dir}/")

        # Print summary
        self._print_training_summary()

    def _print_training_summary(self) -> None:
        """Print training summary"""
        print("\n" + "="*70)
        print("PREDICTIVE MAINTENANCE MODEL TRAINING SUMMARY")
        print("="*70)
        print("✅ Component Health Prediction Models - Trained and saved")
        print("✅ Failure Prediction Model - Trained and saved")
        print("✅ Anomaly Detection Model - Trained and saved")
        print("✅ Label Encoders - Saved")
        print("\nModels trained for components:")
        for component in self.components.keys():
            print(f"   • {component.title()} Health Prediction")
        print("\nModels are ready for integration with the predictive maintenance system!")
        print("="*70)

def main():
    """Main training function"""
    print("Starting Predictive Maintenance Model Training...")

    # Initialize trainer
    trainer = PredictiveMaintenanceModelTrainer()

    # Train all models
    trainer.train_all_models()

    print("Predictive Maintenance Model Training Complete!")

if __name__ == "__main__":
    main()
