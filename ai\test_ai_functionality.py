#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
AI Functionality Test Script

This script tests the core AI features to verify they're working as expected.
It tests each AI module individually without requiring the full service to be running.

Author: Smart Ride-Sharing AI Team
"""

import sys
import os
import json
from datetime import datetime

def test_ride_matching():
    """Test the ride matching functionality"""
    print("🚗 Testing Ride Matching Engine...")
    
    try:
        from advanced_ride_matching import AdvancedRideMatchingEngine
        
        # Initialize the engine
        engine = AdvancedRideMatchingEngine()
        
        # Test data
        ride_request = {
            'pickup_lat': 24.7136,
            'pickup_lng': 46.6753,
            'dropoff_lat': 24.7453,
            'dropoff_lng': 46.6890,
            'passenger_count': 1,
            'vehicle_type_preference': 'standard'
        }
        
        available_drivers = [
            {
                'id': 'driver_1',
                'current_lat': 24.7100,
                'current_lng': 46.6700,
                'rating': 4.5,
                'vehicle_type': 'standard',
                'available_seats': 4,
                'vehicle_year': 2020
            },
            {
                'id': 'driver_2',
                'current_lat': 24.7200,
                'current_lng': 46.6800,
                'rating': 4.8,
                'vehicle_type': 'luxury',
                'available_seats': 3,
                'vehicle_year': 2022
            }
        ]
        
        # Test finding matches
        matches = engine.find_optimal_matches(ride_request, available_drivers)
        
        if matches:
            print(f"   ✅ Found {len(matches)} driver matches")
            print(f"   ✅ Best match: Driver {matches[0]['driver_id']} (Score: {matches[0]['match_score']:.2f})")
            return True
        else:
            print("   ❌ No matches found")
            return False
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def test_dynamic_pricing():
    """Test the dynamic pricing functionality"""
    print("💰 Testing Dynamic Pricing Engine...")
    
    try:
        from dynamic_pricing_model import DynamicPricingEngine
        
        # Initialize the engine
        engine = DynamicPricingEngine()
        
        # Test data
        ride_request = {
            'distance_km': 10.5,
            'duration_minutes': 25,
            'pickup_lat': 24.7136,
            'pickup_lng': 46.6753,
            'dropoff_lat': 24.7453,
            'dropoff_lng': 46.6890,
            'vehicle_type': 'standard',
            'time_of_day': datetime.now().hour
        }
        
        market_data = {
            'demand_level': 'high',
            'supply_level': 'medium',
            'weather_condition': 'clear',
            'is_peak_hour': True,
            'special_events': False
        }
        
        # Test pricing calculation
        pricing_result = engine.calculate_dynamic_price(ride_request, market_data)
        
        if pricing_result and 'base_price' in pricing_result:
            print(f"   ✅ Base price calculated: {pricing_result['base_price']:.2f} SAR")
            print(f"   ✅ Final price: {pricing_result.get('final_price', 'N/A')} SAR")
            return True
        else:
            print("   ❌ Failed to calculate pricing")
            return False
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def test_predictive_maintenance():
    """Test the predictive maintenance functionality"""
    print("🔧 Testing Predictive Maintenance System...")
    
    try:
        from predictive_maintenance import PredictiveMaintenanceSystem
        
        # Initialize the system
        system = PredictiveMaintenanceSystem()
        
        # Test data
        vehicle_id = "VEH_001"
        telemetry_data = {
            'engine_rpm': 2500,
            'oil_pressure': 35,
            'coolant_temperature': 95,
            'brake_pad_thickness': 8,
            'tire_pressure': [32, 31, 32, 31],
            'battery_voltage': 12.4,
            'mileage': 85000,
            'last_service_km': 80000
        }
        
        # Test telemetry analysis
        analysis = system.analyze_telemetry_data(vehicle_id, telemetry_data)
        
        if analysis and 'overall_health_score' in analysis:
            print(f"   ✅ Vehicle health score: {analysis['overall_health_score']:.1f}/100")
            print(f"   ✅ Maintenance recommendations: {len(analysis.get('recommendations', []))} items")
            return True
        else:
            print("   ❌ Failed to analyze telemetry")
            return False
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def test_voice_assistant():
    """Test the voice assistant functionality"""
    print("🎤 Testing Enhanced Voice Assistant...")
    
    try:
        from enhanced_voice_assistant import EnhancedVoiceAssistant
        
        # Initialize the assistant
        assistant = EnhancedVoiceAssistant()
        
        # Test text processing (simulating voice command)
        test_commands = [
            "Book a ride to the airport",
            "احجز لي رحلة إلى المطار",  # Arabic
            "Cancel my current ride",
            "What's my driver's location?"
        ]
        
        successful_tests = 0
        for command in test_commands:
            try:
                # Simulate processing a voice command
                result = assistant.process_text_command(command, user_id="test_user")
                if result and 'intent' in result:
                    successful_tests += 1
            except:
                pass
        
        if successful_tests > 0:
            print(f"   ✅ Processed {successful_tests}/{len(test_commands)} voice commands")
            return True
        else:
            print("   ❌ Failed to process voice commands")
            return False
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def test_safety_monitoring():
    """Test the safety monitoring functionality"""
    print("🛡️ Testing Safety Monitoring System...")
    
    try:
        from safety_monitoring import SafetyMonitoringSystem
        
        # Initialize the system
        system = SafetyMonitoringSystem()
        
        # Test data
        ride_data = {
            'ride_id': 'RIDE_001',
            'driver_id': 'DRIVER_001',
            'rider_id': 'RIDER_001',
            'pickup_location': (24.7136, 46.6753),
            'dropoff_location': (24.7453, 46.6890),
            'estimated_duration': 25
        }
        
        # Test starting monitoring
        result = system.start_ride_monitoring(ride_data)
        
        if result and result.get('monitoring_started'):
            print("   ✅ Safety monitoring started successfully")
            
            # Test location update
            location_data = {
                'current_location': (24.7150, 46.6760),
                'speed': 45,
                'timestamp': datetime.now().isoformat()
            }
            
            update_result = system.update_ride_location('RIDE_001', location_data)
            if update_result:
                print("   ✅ Location update processed successfully")
                return True
        
        print("   ❌ Failed to start safety monitoring")
        return False
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def test_conversational_ai():
    """Test the conversational AI functionality"""
    print("💬 Testing Conversational AI...")
    
    try:
        from conversational_ai import ConversationalAI
        
        # Initialize the AI
        ai = ConversationalAI()
        
        # Test conversations
        test_messages = [
            "Hello, I need help with my ride",
            "Where is my driver?",
            "I want to change my destination",
            "Thank you for your help"
        ]
        
        successful_responses = 0
        for message in test_messages:
            try:
                result = ai.process_conversation(
                    user_id="test_user",
                    message=message,
                    intent=None,
                    entities={}
                )
                if result and 'response' in result:
                    successful_responses += 1
            except:
                pass
        
        if successful_responses > 0:
            print(f"   ✅ Generated {successful_responses}/{len(test_messages)} responses")
            return True
        else:
            print("   ❌ Failed to generate responses")
            return False
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def main():
    """Run all AI functionality tests"""
    print("🤖 Smart Ride-Sharing AI Functionality Test")
    print("=" * 50)
    
    tests = [
        ("Ride Matching", test_ride_matching),
        ("Dynamic Pricing", test_dynamic_pricing),
        ("Predictive Maintenance", test_predictive_maintenance),
        ("Voice Assistant", test_voice_assistant),
        ("Safety Monitoring", test_safety_monitoring),
        ("Conversational AI", test_conversational_ai)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"   ❌ Test failed with exception: {e}")
            results.append((test_name, False))
        print()
    
    # Summary
    print("📊 Test Results Summary:")
    print("-" * 30)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:20} {status}")
        if result:
            passed += 1
    
    print("-" * 30)
    print(f"Total: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 All AI features are working correctly!")
        return True
    elif passed > total // 2:
        print(f"\n⚠️  Most AI features are working ({passed}/{total} passed)")
        return True
    else:
        print(f"\n❌ Multiple AI features need attention ({passed}/{total} passed)")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
