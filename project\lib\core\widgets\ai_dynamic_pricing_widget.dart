import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/ai_ride_provider.dart';
import '../models/ride_request.dart';
import '../theme/app_colors.dart';

/// AI-powered dynamic pricing widget with detailed breakdown
class AIDynamicPricingWidget extends StatefulWidget {
  final RideRequest rideRequest;
  final Map<String, dynamic>? marketData;
  final Function(Map<String, dynamic>)? onPriceCalculated;

  const AIDynamicPricingWidget({
    Key? key,
    required this.rideRequest,
    this.marketData,
    this.onPriceCalculated,
  }) : super(key: key);

  @override
  State<AIDynamicPricingWidget> createState() => _AIDynamicPricingWidgetState();
}

class _AIDynamicPricingWidgetState extends State<AIDynamicPricingWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _showPriceBreakdown = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.elasticOut),
    );
    
    _calculateAIPricing();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _calculateAIPricing() async {
    final aiProvider = Provider.of<AIRideProvider>(context, listen: false);
    final pricing = await aiProvider.calculateAIDynamicPrice(
      rideRequest: widget.rideRequest,
      marketData: widget.marketData,
    );
    
    if (pricing != null && widget.onPriceCalculated != null) {
      widget.onPriceCalculated!(pricing);
    }
    
    _animationController.forward();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AIRideProvider>(
      builder: (context, aiProvider, child) {
        final pricing = aiProvider.dynamicPricing;
        
        return ScaleTransition(
          scale: _scaleAnimation,
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  AppColors.primary.withOpacity(0.1),
                  AppColors.secondary.withOpacity(0.1),
                ],
              ),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: AppColors.primary.withOpacity(0.3),
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildHeader(aiProvider),
                if (aiProvider.isLoading) _buildLoadingState(),
                if (!aiProvider.isLoading && pricing != null)
                  _buildPricingDisplay(pricing),
                if (!aiProvider.isLoading && pricing == null)
                  _buildNoPricingState(),
                if (_showPriceBreakdown && pricing != null)
                  _buildPriceBreakdown(pricing),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeader(AIRideProvider aiProvider) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.primary.withOpacity(0.1),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppColors.primary,
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(
              Icons.attach_money,
              color: Colors.white,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  aiProvider.aiServiceConnected 
                    ? 'AI Dynamic Pricing'
                    : 'Standard Pricing',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  aiProvider.aiServiceConnected 
                    ? 'Real-time market analysis'
                    : 'Fixed rate calculation',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          if (aiProvider.dynamicPricing != null)
            IconButton(
              onPressed: () {
                setState(() {
                  _showPriceBreakdown = !_showPriceBreakdown;
                });
              },
              icon: Icon(
                _showPriceBreakdown ? Icons.expand_less : Icons.expand_more,
                color: AppColors.primary,
              ),
            ),
          IconButton(
            onPressed: _calculateAIPricing,
            icon: const Icon(Icons.refresh, color: AppColors.primary),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingState() {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          const CircularProgressIndicator(color: AppColors.primary),
          const SizedBox(height: 16),
          Text(
            'AI is calculating optimal pricing...',
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPricingDisplay(Map<String, dynamic> pricing) {
    final finalPrice = pricing['final_price'] ?? 0.0;
    final basePrice = pricing['base_price'] ?? 0.0;
    final surgeMultiplier = pricing['surge_multiplier'] ?? 1.0;
    final isAIPowered = pricing['ai_powered'] ?? false;
    final explanation = pricing['price_explanation'] ?? '';

    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Main price display
          Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                '${finalPrice.toStringAsFixed(2)}',
                style: const TextStyle(
                  fontSize: 32,
                  fontWeight: FontWeight.bold,
                  color: AppColors.primary,
                ),
              ),
              const SizedBox(width: 8),
              Padding(
                padding: const EdgeInsets.only(bottom: 4),
                child: Text(
                  'SAR',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: Colors.grey[600],
                  ),
                ),
              ),
              const Spacer(),
              if (isAIPowered)
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.success,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Text(
                    'AI Optimized',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          // Price comparison
          if (basePrice != finalPrice) ...[
            Row(
              children: [
                Text(
                  'Base Price: ',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
                Text(
                  '${basePrice.toStringAsFixed(2)} SAR',
                  style: TextStyle(
                    fontSize: 14,
                    decoration: TextDecoration.lineThrough,
                    color: Colors.grey[500],
                  ),
                ),
                const SizedBox(width: 8),
                if (surgeMultiplier > 1.0)
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 6,
                      vertical: 2,
                    ),
                    decoration: BoxDecoration(
                      color: _getSurgeColor(surgeMultiplier),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      '${surgeMultiplier.toStringAsFixed(1)}x',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 8),
          ],
          
          // Price explanation
          if (explanation.isNotEmpty) ...[
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.info.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: AppColors.info.withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    size: 16,
                    color: AppColors.info,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      explanation,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[700],
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 12),
          ],
          
          // Trip details
          _buildTripDetails(),
        ],
      ),
    );
  }

  Widget _buildTripDetails() {
    final distance = widget.rideRequest.estimatedDistance ?? 0.0;
    final duration = widget.rideRequest.estimatedDuration ?? 0;
    
    return Row(
      children: [
        Expanded(
          child: _buildDetailItem(
            Icons.straighten,
            'Distance',
            '${distance.toStringAsFixed(1)} km',
          ),
        ),
        Expanded(
          child: _buildDetailItem(
            Icons.access_time,
            'Duration',
            '${duration} min',
          ),
        ),
        Expanded(
          child: _buildDetailItem(
            Icons.directions_car,
            'Type',
            widget.rideRequest.vehicleType ?? 'Standard',
          ),
        ),
      ],
    );
  }

  Widget _buildDetailItem(IconData icon, String label, String value) {
    return Column(
      children: [
        Icon(
          icon,
          size: 20,
          color: AppColors.primary,
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            fontSize: 10,
            color: Colors.grey[600],
          ),
        ),
        Text(
          value,
          style: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildPriceBreakdown(Map<String, dynamic> pricing) {
    final basePrice = pricing['base_price'] ?? 0.0;
    final surgeMultiplier = pricing['surge_multiplier'] ?? 1.0;
    final finalPrice = pricing['final_price'] ?? 0.0;
    
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.grey.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Price Breakdown',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          _buildBreakdownItem('Base Fare', basePrice),
          if (surgeMultiplier > 1.0)
            _buildBreakdownItem(
              'Surge (${surgeMultiplier.toStringAsFixed(1)}x)',
              finalPrice - basePrice,
            ),
          const Divider(),
          _buildBreakdownItem('Total', finalPrice, isTotal: true),
        ],
      ),
    );
  }

  Widget _buildBreakdownItem(String label, double amount, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: isTotal ? 14 : 12,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              color: isTotal ? AppColors.primary : Colors.grey[700],
            ),
          ),
          Text(
            '${amount.toStringAsFixed(2)} SAR',
            style: TextStyle(
              fontSize: isTotal ? 14 : 12,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              color: isTotal ? AppColors.primary : Colors.grey[700],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNoPricingState() {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          Icon(
            Icons.error_outline,
            size: 48,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'Unable to calculate pricing',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Please try again or contact support',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Color _getSurgeColor(double multiplier) {
    if (multiplier >= 2.0) return AppColors.error;
    if (multiplier >= 1.5) return AppColors.warning;
    return AppColors.info;
  }
}
