import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import '../models/ride_request.dart';
import '../models/driver_model.dart';

/// AI Service for integrating with the Smart Ride-Sharing AI backend
class AIService {
  static const String _baseUrl = 'http://localhost:5001/api/ai';
  static const Duration _timeout = Duration(seconds: 30);

  /// Common headers for all requests
  static const Map<String, String> _headers = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };

  /// Get AI service status
  static Future<Map<String, dynamic>?> getServiceStatus() async {
    try {
      final response = await http
          .get(
            Uri.parse('$_baseUrl/status'),
            headers: _headers,
          )
          .timeout(_timeout);

      if (response.statusCode == 200) {
        return json.decode(response.body);
      }
      return null;
    } catch (e) {
      debugPrint('AI Service Status Error: $e');
      return null;
    }
  }

  /// Find optimal driver matches using AI
  static Future<List<Map<String, dynamic>>> findOptimalMatches({
    required RideRequest rideRequest,
    required List<DriverModel> availableDrivers,
    List<Map<String, dynamic>>? existingRides,
  }) async {
    try {
      final requestData = {
        'ride_request': {
          'pickup_lat': rideRequest.pickupLocation['latitude'] ?? 0.0,
          'pickup_lng': rideRequest.pickupLocation['longitude'] ?? 0.0,
          'dropoff_lat': rideRequest.destination['latitude'] ?? 0.0,
          'dropoff_lng': rideRequest.destination['longitude'] ?? 0.0,
          'passenger_count': rideRequest.passengerCount ?? 1,
          'vehicle_type_preference': rideRequest.vehicleType ?? 'standard',
          'urgency_level': rideRequest.urgencyLevel ?? 'normal',
        },
        'available_drivers': availableDrivers
            .map((driver) => {
                  'id': driver.id,
                  'current_lat': driver.location.latitude,
                  'current_lng': driver.location.longitude,
                  'rating': driver.rating,
                  'vehicle_type': driver.car.make,
                  'available_seats': 4, // Default value
                  'vehicle_year': driver.car.year,
                })
            .toList(),
        'existing_rides': existingRides ?? [],
      };

      final response = await http
          .post(
            Uri.parse('$_baseUrl/ride-matching/find-matches'),
            headers: _headers,
            body: json.encode(requestData),
          )
          .timeout(_timeout);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          return List<Map<String, dynamic>>.from(data['matches'] ?? []);
        }
      }
      return [];
    } catch (e) {
      debugPrint('AI Find Matches Error: $e');
      return [];
    }
  }

  /// Calculate dynamic pricing using AI
  static Future<Map<String, dynamic>?> calculateDynamicPrice({
    required RideRequest rideRequest,
    Map<String, dynamic>? marketData,
  }) async {
    try {
      final requestData = {
        'ride_request': {
          'distance_km': rideRequest.estimatedDistance ?? 0.0,
          'duration_minutes': rideRequest.estimatedDuration ?? 0,
          'pickup_lat': rideRequest.pickupLocation['latitude'] ?? 0.0,
          'pickup_lng': rideRequest.pickupLocation['longitude'] ?? 0.0,
          'dropoff_lat': rideRequest.destination['latitude'] ?? 0.0,
          'dropoff_lng': rideRequest.destination['longitude'] ?? 0.0,
          'vehicle_type': rideRequest.vehicleType ?? 'standard',
          'time_of_day': DateTime.now().hour,
          'day_of_week': DateTime.now().weekday,
        },
        'market_data': marketData ??
            {
              'demand_level': 'medium',
              'supply_level': 'medium',
              'weather_condition': 'clear',
              'is_peak_hour': _isPeakHour(),
              'special_events': false,
            },
      };

      final response = await http
          .post(
            Uri.parse('$_baseUrl/pricing/calculate-price'),
            headers: _headers,
            body: json.encode(requestData),
          )
          .timeout(_timeout);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          return data['pricing'];
        }
      }
      return null;
    } catch (e) {
      debugPrint('AI Dynamic Pricing Error: $e');
      return null;
    }
  }

  /// Analyze vehicle telemetry for predictive maintenance
  static Future<Map<String, dynamic>?> analyzeVehicleTelemetry({
    required String vehicleId,
    required Map<String, dynamic> telemetryData,
  }) async {
    try {
      final requestData = {
        'vehicle_id': vehicleId,
        'telemetry_data': telemetryData,
      };

      final response = await http
          .post(
            Uri.parse('$_baseUrl/maintenance/analyze-telemetry'),
            headers: _headers,
            body: json.encode(requestData),
          )
          .timeout(_timeout);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          return data['analysis'];
        }
      }
      return null;
    } catch (e) {
      debugPrint('AI Telemetry Analysis Error: $e');
      return null;
    }
  }

  /// Start safety monitoring for a ride
  static Future<Map<String, dynamic>?> startSafetyMonitoring({
    required String rideId,
    required String driverId,
    required String riderId,
    required Map<String, double> pickupLocation,
    required Map<String, double> dropoffLocation,
    required int estimatedDuration,
  }) async {
    try {
      final requestData = {
        'ride_data': {
          'ride_id': rideId,
          'driver_id': driverId,
          'rider_id': riderId,
          'pickup_location': [pickupLocation['lat'], pickupLocation['lng']],
          'dropoff_location': [dropoffLocation['lat'], dropoffLocation['lng']],
          'estimated_duration': estimatedDuration,
        },
      };

      final response = await http
          .post(
            Uri.parse('$_baseUrl/safety/start-monitoring'),
            headers: _headers,
            body: json.encode(requestData),
          )
          .timeout(_timeout);

      if (response.statusCode == 200) {
        return json.decode(response.body);
      }
      return null;
    } catch (e) {
      debugPrint('AI Safety Monitoring Error: $e');
      return null;
    }
  }

  /// Update ride location for safety monitoring
  static Future<Map<String, dynamic>?> updateRideLocation({
    required String rideId,
    required double latitude,
    required double longitude,
    required double speed,
  }) async {
    try {
      final requestData = {
        'ride_id': rideId,
        'location_data': {
          'current_location': [latitude, longitude],
          'speed': speed,
          'timestamp': DateTime.now().toIso8601String(),
        },
      };

      final response = await http
          .post(
            Uri.parse('$_baseUrl/safety/update-location'),
            headers: _headers,
            body: json.encode(requestData),
          )
          .timeout(_timeout);

      if (response.statusCode == 200) {
        return json.decode(response.body);
      }
      return null;
    } catch (e) {
      debugPrint('AI Location Update Error: $e');
      return null;
    }
  }

  /// Process smart ride request using multiple AI systems
  static Future<Map<String, dynamic>?> processSmartRideRequest({
    required RideRequest rideRequest,
    required List<DriverModel> availableDrivers,
    Map<String, dynamic>? marketData,
  }) async {
    try {
      final requestData = {
        'ride_request': {
          'pickup_lat': rideRequest.pickupLocation['latitude'] ?? 0.0,
          'pickup_lng': rideRequest.pickupLocation['longitude'] ?? 0.0,
          'dropoff_lat': rideRequest.destination['latitude'] ?? 0.0,
          'dropoff_lng': rideRequest.destination['longitude'] ?? 0.0,
          'passenger_count': rideRequest.passengerCount ?? 1,
          'vehicle_type_preference': rideRequest.vehicleType ?? 'standard',
          'distance_km': rideRequest.estimatedDistance ?? 0.0,
          'duration_minutes': rideRequest.estimatedDuration ?? 0,
        },
        'market_data': marketData ??
            {
              'demand_level': 'medium',
              'supply_level': 'medium',
              'weather_condition': 'clear',
              'is_peak_hour': _isPeakHour(),
              'special_events': false,
            },
        'available_drivers': availableDrivers
            .map((driver) => {
                  'id': driver.id,
                  'current_lat': driver.location.latitude,
                  'current_lng': driver.location.longitude,
                  'rating': driver.rating,
                  'vehicle_type': driver.car.make,
                  'available_seats': 4, // Default value
                  'vehicle_year': driver.car.year,
                })
            .toList(),
      };

      final response = await http
          .post(
            Uri.parse('$_baseUrl/integrated/smart-ride-request'),
            headers: _headers,
            body: json.encode(requestData),
          )
          .timeout(_timeout);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          return data;
        }
      }
      return null;
    } catch (e) {
      debugPrint('AI Smart Ride Request Error: $e');
      return null;
    }
  }

  /// Get AI insights for driver dashboard
  static Future<Map<String, dynamic>?> getDriverDashboardInsights({
    required String driverId,
    Map<String, dynamic>? driverData,
    Map<String, dynamic>? vehicleData,
    Map<String, dynamic>? marketData,
  }) async {
    try {
      final requestData = {
        'driver_id': driverId,
        'driver_data': driverData ?? {},
        'vehicle_data': vehicleData ?? {},
        'market_data': marketData ??
            {
              'demand_level': 'medium',
              'supply_level': 'medium',
              'weather_condition': 'clear',
              'is_peak_hour': _isPeakHour(),
            },
      };

      final response = await http
          .post(
            Uri.parse('$_baseUrl/integrated/driver-dashboard'),
            headers: _headers,
            body: json.encode(requestData),
          )
          .timeout(_timeout);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          return data;
        }
      }
      return null;
    } catch (e) {
      debugPrint('AI Driver Dashboard Error: $e');
      return null;
    }
  }

  /// Check if current time is peak hour
  static bool _isPeakHour() {
    final hour = DateTime.now().hour;
    return (hour >= 7 && hour <= 9) || (hour >= 17 && hour <= 19);
  }

  /// Test AI service connectivity
  static Future<bool> testConnection() async {
    try {
      final status = await getServiceStatus();
      return status != null && status['status'] == 'running';
    } catch (e) {
      debugPrint('AI Service Connection Test Failed: $e');
      return false;
    }
  }

  /// Get fallback pricing when AI service is unavailable
  static Map<String, dynamic> getFallbackPricing({
    required double distance,
    required int duration,
    String vehicleType = 'standard',
  }) {
    // Simple rule-based pricing as fallback
    double basePrice = 5.0; // Base fare
    double distancePrice = distance * 2.0; // Per km
    double timePrice = duration * 0.5; // Per minute

    // Vehicle type multiplier
    double multiplier = 1.0;
    switch (vehicleType.toLowerCase()) {
      case 'premium':
        multiplier = 1.5;
        break;
      case 'suv':
        multiplier = 1.3;
        break;
      case 'economy':
        multiplier = 0.8;
        break;
      default:
        multiplier = 1.0;
    }

    double finalPrice = (basePrice + distancePrice + timePrice) * multiplier;

    return {
      'base_price': basePrice,
      'distance_price': distancePrice,
      'time_price': timePrice,
      'multiplier': multiplier,
      'final_price': finalPrice,
      'currency': 'SAR',
      'is_fallback': true,
      'breakdown': {
        'base_fare': basePrice,
        'distance_fare': distancePrice,
        'time_fare': timePrice,
        'vehicle_multiplier': multiplier,
      }
    };
  }

  /// Get fallback driver matches when AI service is unavailable
  static List<Map<String, dynamic>> getFallbackMatches({
    required List<DriverModel> availableDrivers,
    required RideRequest rideRequest,
  }) {
    // Simple distance-based matching as fallback
    final pickupLat = rideRequest.pickupLocation['latitude'] ?? 0.0;
    final pickupLng = rideRequest.pickupLocation['longitude'] ?? 0.0;

    return availableDrivers.map((driver) {
      // Calculate simple distance (not accurate, but good for fallback)
      final driverLat = driver.location.latitude;
      final driverLng = driver.location.longitude;
      final distance =
          _calculateSimpleDistance(pickupLat, pickupLng, driverLat, driverLng);

      return {
        'driver_id': driver.id,
        'driver_name': driver.name,
        'rating': driver.rating,
        'distance_km': distance,
        'estimated_arrival':
            (distance * 2).round(), // Rough estimate in minutes
        'vehicle_info': {
          'make': driver.car.make,
          'model': driver.car.model,
          'color': driver.car.color,
          'plate': driver.car.plateNumber,
        },
        'match_score': (100 - (distance * 10)).clamp(0, 100), // Simple scoring
        'is_fallback': true,
      };
    }).toList()
      ..sort((a, b) {
        final distanceA = a['distance_km'] as double;
        final distanceB = b['distance_km'] as double;
        return distanceA.compareTo(distanceB);
      }); // Sort by distance
  }

  /// Simple distance calculation for fallback
  static double _calculateSimpleDistance(
      double lat1, double lng1, double lat2, double lng2) {
    // Very simple distance calculation (not accurate, just for fallback)
    final deltaLat = lat2 - lat1;
    final deltaLng = lng2 - lng1;
    return (deltaLat * deltaLat + deltaLng * deltaLng) *
        111; // Rough km conversion
  }
}
