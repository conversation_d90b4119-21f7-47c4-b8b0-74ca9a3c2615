{"checksum": "429a87443dddad227c68f2a90b702516", "roots": {"bookmark_bar": {"children": [{"date_added": "13373753705520724", "date_last_used": "0", "guid": "bd993b52-d375-4b78-a7cb-88ca72ce91aa", "id": "6", "name": "Real-Time Location Tracking System with Flutter, Node.js and Socket.IO | by <PERSON><PERSON><PERSON> | Medium", "show_icon": false, "source": "sync", "type": "url", "url": "https://medium.com/@berkekurnaz/real-time-location-tracking-system-with-flutter-node-js-and-socket-io-0f00579ce629", "visit_count": 0}, {"date_added": "13383073239427624", "date_last_used": "0", "guid": "50f287f7-c26e-478a-92bd-d779420f781b", "id": "7", "name": "https://www.figma.com/design/z1eDlc4IJh1mbh8iYWG9Ia/UBER-APP-DESIGN-%26-PROTOTYPE-(Community)?node-id=33-6&t=6LUJMrr3VXhDvH9t-0", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.figma.com/design/z1eDlc4IJh1mbh8iYWG9Ia/UBER-APP-DESIGN-%26-PROTOTYPE-(Community)?node-id=33-6&t=6LUJMrr3VXhDvH9t-0", "visit_count": 0}, {"date_added": "13383073256096872", "date_last_used": "0", "guid": "74022ca2-2661-4690-ab51-3f1c2dfd6210", "id": "8", "name": "https://www.dailymotion.com/video/x8ruv99", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.dailymotion.com/video/x8ruv99", "visit_count": 0}, {"date_added": "13383747742723182", "date_last_used": "13393005128355886", "guid": "56c781a5-a98f-471e-afa7-4527cd201ce1", "id": "9", "name": "https://open.spotify.com/", "show_icon": false, "source": "sync", "type": "url", "url": "https://open.spotify.com/", "visit_count": 83}, {"date_added": "13384028299448785", "date_last_used": "0", "guid": "c88dbb85-fa60-4145-a3e7-f6633911a3b1", "id": "10", "name": "https://docs.github.com/en/get-started/writing-on-github/getting-started-with-writing-and-formatting-on-github/basic-writing-and-formatting-syntax", "show_icon": false, "source": "sync", "type": "url", "url": "https://docs.github.com/en/get-started/writing-on-github/getting-started-with-writing-and-formatting-on-github/basic-writing-and-formatting-syntax", "visit_count": 0}, {"date_added": "13384181386358317", "date_last_used": "0", "guid": "bd536374-535f-45a8-809c-e7e6ac11abd0", "id": "11", "name": "https://www.pradipdebnath.com/2020/10/04/how-to-implement-email-authentication-in-react-native-using-firebase/", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.pradipdebnath.com/2020/10/04/how-to-implement-email-authentication-in-react-native-using-firebase/", "visit_count": 0}, {"date_added": "13384605623838882", "date_last_used": "0", "guid": "08d2dc9f-8927-4ce1-85e8-d4dc442cdf61", "id": "12", "name": "https://pub.dev/", "show_icon": false, "source": "sync", "type": "url", "url": "https://pub.dev/", "visit_count": 0}, {"date_added": "13385049413183467", "date_last_used": "0", "guid": "eaf89f92-a658-4322-aaaf-16443884bc34", "id": "13", "name": "https://play.google.com/console/u/1/developers/7931969641797171837/app/4972881873366431155/policy-center/issues/4987136894415054053/details", "show_icon": false, "source": "sync", "type": "url", "url": "https://play.google.com/console/u/1/developers/7931969641797171837/app/4972881873366431155/policy-center/issues/4987136894415054053/details", "visit_count": 0}, {"date_added": "13385498158558517", "date_last_used": "13385553279990045", "guid": "a87c4578-236c-4f34-a179-31d425a74188", "id": "14", "name": "https://github.com/signup?return_to=https%3A%2F%2Fgithub.com%2FDhiWise&source=login", "show_icon": false, "source": "sync", "type": "url", "url": "https://github.com/signup?return_to=https%3A%2F%2Fgithub.com%2FDhiWise&source=login", "visit_count": 1}, {"date_added": "13385642104522910", "date_last_used": "0", "guid": "1038085b-e222-4b82-9fcd-44798dab5063", "id": "15", "name": "https://bitbucket.org/gogrow-development/ds-stay-app/src/master/", "show_icon": false, "source": "sync", "type": "url", "url": "https://bitbucket.org/gogrow-development/ds-stay-app/src/master/", "visit_count": 0}, {"date_added": "13386725588346268", "date_last_used": "0", "guid": "314d50c9-4f3d-46c4-abd2-643051b46264", "id": "16", "meta_info": {"power_bookmark_meta": ""}, "name": "(94) WhatsApp", "show_icon": false, "source": "sync", "type": "url", "url": "https://web.whatsapp.com/", "visit_count": 1}, {"date_added": "13389744594112988", "date_last_used": "0", "guid": "da8159db-8da7-4216-b148-1c9f62b47b22", "id": "17", "name": "https://www.finalspaceapi.com/docs/", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.finalspaceapi.com/docs/", "visit_count": 0}, {"date_added": "13391294770398697", "date_last_used": "0", "guid": "28a0d684-898c-4995-b969-1ab78c93e5aa", "id": "18", "name": "https://app.wiremock.cloud/mock-apis/create-flow", "show_icon": false, "source": "sync", "type": "url", "url": "https://app.wiremock.cloud/mock-apis/create-flow", "visit_count": 0}], "date_added": "13393019174755782", "date_last_used": "0", "date_modified": "13391294770398697", "guid": "0bc5d13f-2cba-5d74-951f-3f233fe6c908", "id": "1", "name": "Favorites bar", "source": "unknown", "type": "folder"}, "other": {"children": [], "date_added": "13393019174755785", "date_last_used": "0", "date_modified": "0", "guid": "82b081ec-3dd3-529c-8475-ab6c344590dd", "id": "2", "name": "Other favorites", "source": "unknown", "type": "folder"}, "synced": {"children": [], "date_added": "13393019174755787", "date_last_used": "0", "date_modified": "0", "guid": "4cf2e351-0e85-532b-bb37-df045d8f8d0f", "id": "3", "name": "Mobile favorites", "source": "unknown", "type": "folder"}}, "sync_metadata": "CjEKDgiIgQISCH/amByXAQAAEgAqGGU3TUZzWks5UnJlbVpHMitjMStwNkE9PUgDoAYHEq4BCBESqQEKHC9vamlqZmwrWU5uaHp2WUdrQ1B0YUlkeWZWdz0SJGRhODE1OWRiLThkYTctNDIxNi1iMTQ4LTFjOWY2MmI0N2IyMhgAIAAoADC9pYTS5TI45aaE0uUyQOWmhNLlMkocbkM0dDNjQjJyc2FDMm1ueWc5S0xtcU45VVIwPVogIh7/7Ug0RHhxQ0FxNDRiNUJacTV6bWJTQnBjYmhqRT1lydC/oMI+AmAFEq4BCA8SqQEKHEcvMG43ZWQrYi9vbEo3WG1EUnYxMGF3aTludz0SJDEwMzgwODViLWUyMjItNGI4Mi05ZmNkLTQ0Nzk4ZGFiNTA2MxgAIAAoADCc2Oit1jI4mtrordYyQJra6K3WMkocV2Uwb0ZXczFlTEdqd0FBTzZ5UU8rUzZRblpVPVogIh7/tUdXbWdkc3VTaGJENkxCVVZuT0FrZmZuQVpWWT1l4XkbqMI+AmAFEq0BCA4SqAEKHHJNbDhqUXJldDlybFBFN201YnpweDBXNE95TT0SJGE4N2M0NTc4LTIzNmMtNGYzNC1hMTc5LTMxZDQyNWE3NDE4OBgAIAAoADC9qbuD1jI4zPyW6dUyQMz8lunVMkocelM2UnVKNGNOd2g1KzFsWnVtVVJsOUpWR2FjPVofIh3/azN1bmYvWXlXWWYvNFBhM29NeGZiQTNTVmc4PWXI8z+7wj4CYAUSrgEIEBKpAQocWEtuSFZVQ0pJeWFvSDdKcmZtZGkxenQzazE4PRIkMzE0ZDUwYzktNGYzZC00NmM0LWFiZDItNjQzMDUxYjQ2MjY0GAAgACgAMNaZ99/xMjiVnLuy2jJAlZy7stoyShxYZGZVR0FndUpWckZNQXRrRjA0NGtVUFc1TVE9WiAiHv/aaXR4UTVpQS93TDNIeTVFc29CMGJUTmpYbjVvPWWjGU+twj4CYAUSrQEIDBKoAQocQU1NaDFhU0d6NFFabldZNlFiZEVtTUU2ODc4PRIkMDhkMmRjOWYtODkyNy00Y2UxLTg1ZTgtZDRkYzQ0MmNkZjYxGAAgACgAMJizy7/SMjiWtsu/0jJAlrbLv9IyShxIcEhHUTBNRnJqamVGWXBDblVFYjBRU1BWcVk9Wh8iHf1xeDh5UlBNT1NHc1c2M0IrdklTUlphMDhKWGc9ZareUi3CPgJgBRKOAQgDEokBChxuamdEdTg4ZUNydlhwODZwRDdmSEdjcUZwODg9EiQyOTZmMGMxMi03MzliLTUwM2ItOWE4MC1kZjQ1OWMzY2IxMTIYACAAKAAwrN+E4akyOJ7ihOGpMkCe4oThqTJKHHl6TkIwMWNuNTRBN2NSVTB1VCsvUGdtVHRVbz1aAGUAAAAAwj4CYAASrQEICxKoAQocb2dWeTVFMFVUNjJxUkNITmF6V01RdVFoaGZVPRIkYmQ1MzYzNzQtNTM1Zi00NWE4LTgwOWMtZTdlNmFjMTFhYmQwGAAgACgAMKCapvXQMjiSm6b10DJAkpum9dAyShxJNG16Y1lxRXlvaFFmTFlGOFl6QTlpUkdLV0k9Wh8iHftuTkY2bm9KSFF0UUJNdmNFMVFpV2tCejRyRWs9ZYkCm9vCPgJgBRKtAQgKEqgBChxNSmRCYkdGRzBtWjRwS3BnM1pveGowVTJQRHM9EiRjODhkYmI4NS1mYTYwLTQxNDUtYTNlNy1mNjYzMzkxMWEzYjEYACAAKAAw/8KmrNAyOIrGpqzQMkCKxqas0DJKHHBla05ycDQ3MzlYWi8xNDE0SU1JQStpQ2FGRT1aHyId9zlOc3czTW93SUp2UWpLZ0EyRkpFcWpseUpLVT1lN+Eap8I+AmAFEq0BCAkSqAEKHHFCMTAveG5CbnNVRzZsNWVmQzlsMXVWaGdMMD0SJDU2Yzc4MWE1LWE5OGYtNDcxZS1hZmE3LTQ1MjdjZDIwMWNlMRgAIAAoADD+tOPk8TI4iOrCps8yQIjqwqbPMkocclZOWVVKc1c5K0tCUldOQnpqWnRhQlNQdFBJPVofIh3udHFldDRabkNhaWcrcll4RzdNNVJNMmZJdWZvPWUPYiZ4wj4CYAUSrQEICBKoAQocVysreDFVSDA0ZkNnQ0tCYkRXcEpDQTV4TjdVPRIkNzQwMjJjYTItMjY2MS00NjkwLWFiNTEtM2YxYzJkZmQ2MjEwGAAgACgAMOPR8+TMMjip0vPkzDJAqdLz5MwyShxFQ3krZzk5WSs3VHhudjlLNm5Bd3llZC92aUU9Wh8iHdxvalRjdDltd21nd0RwaEpnTFhRUmN4ek9TZEU9Zd3dfFfCPgJgBRKtAQgHEqgBChxNMTM4NTdTVmF2elRUdGVRRFI1T1VTemVXRUU9EiQ1MGYyODdmNy1jMjZlLTQ3OGEtOTJiZC1kNzc5NDIwZjc4MWIYACAAKAAwkdDy5MwyOL/R8uTMMkC/0fLkzDJKHDJ0eUkyb1VoWFRacUtVWmZWTmVrczNRTWtTMD1aHyIduHBUYVRwOHVTdTlMWUpudnFrZ2J2OWVSY2J3TT1l7wCOh8I+AmAFEqwBCAYSpwEKHE5lYVdJZ0ZPeGhLY0t0TEVSSGd2czF0YkE0bz0SJGJkOTkzYjUyLWQzNzUtNGI3OC1hN2NiLTg4Y2E3MmNlOTFhYRgAIAAoADCrl4KJqjI415qCiaoyQNeagomqMkocN0k4YlBpVVpneHdnbFJTTnV0dXdxS21JZzc0PVoeIhxwdklDaUFiT3lMelhGM09BWlJPOUNWb1Z5VlU9ZatNfMvCPgJgBRKOAQgBEokBChx5eEovNmZ2a2VjcjdrcDVFUXpCNHRXTHBkYkU9EiRmNjg0ZjJkYy0zMmRjLTU3MTEtYTRjNi01ODU5ZmY1MzVmMzcYACAAKAAwpt+E4akyOMfhhOGpMkDH4YThqTJKHGFSQ1dMZ2hGOWI1SW5xS0lEdnVKL2p2UWtGYz1aAGUAAAAAwj4CYAASrgEIEhKpAQocMUZ4QUM0YXl6UTlKT1I0NFM5SWI1bjdnNEtZPRIkMjhhMGQ2ODQtODk4Yy00OTk1LWI5NjktMWFiNzhjOTNlNWFhGAAgACgAMLmum7XrMjiwsJu16zJAsLCbtesyShxrM0l3Zno3d01RWmpJSFhmL25TK1BDVmJoR3c9WiAiHv/2Q3JCTHplK0o3ZFE2bnpMdjVUTFhuV1pCb2VZPWXrlNNJwj4CYAUSjgEIAhKJAQocWWNTVG9udkVTUHdxQ3M0TDJjclZodjY4TDk4PRIkYmI2NjJhYzgtMTM1NS01ZTdkLTg3ZTctYzk4ZGIxZDgxYTQ3GAAgACgAMKffhOGpMjjk4YThqTJA5OGE4akyShxvUFZraHdCUmJWNjVLQ1dSMitJT05IV29PdTg9WgBlAAAAAMI+AmAAEq0BCA0SqAEKHEo3ZzNONVZvc0Fkem1peEFpOWRRSlQvd1hVYz0SJGVhZjg5ZjkyLWE2NTgtNDMyMi1hYWFmLTE2NDQzODg0YmMzNBgAIAAoADDC+JmT1DI46fmZk9QyQOn5mZPUMkoceUlqRFllUkl6UEVZRTRDR3ovQ1Myd3hLaUg0PVofIh3+cVluYy9KUTlMTHhBazBzeHNrZTdub0IvTHNrPWVd0vQywj4CYAUwADgA", "version": 1}