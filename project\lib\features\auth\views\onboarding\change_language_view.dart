import 'package:flutter/material.dart';
import 'package:project/core/constants/my_colors.dart';
import 'package:project/widgets/voice_assistant_button.dart';

import 'onboarding_screen.dart';

class ChangeLanguageView extends StatefulWidget {
  const ChangeLanguageView({super.key});

  @override
  State<ChangeLanguageView> createState() => _ChangeLanguageViewState();
}

class _ChangeLanguageViewState extends State<ChangeLanguageView>
    with SingleTickerProviderStateMixin {
  final List<Map<String, dynamic>> languages = [
    {"name": "Arabic", "code": "ar", "flag": "🇸🇦"},
    {"name": "English", "code": "en", "flag": "🇺🇸"},
    {"name": "French", "code": "fr", "flag": "🇫🇷"},
    {"name": "German", "code": "de", "flag": "🇩🇪"},
    {"name": "Spanish", "code": "es", "flag": "🇪🇸"},
  ];

  int selectedLanguageIndex = 1;

  // Animation controller
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();

    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1000),
    );

    // Create fade animation
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );

    // Start animation
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  // Navigate to onboarding screen
  void _navigateToOnboarding() {
    Navigator.of(context).pushReplacement(
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            const OnboardingScreen(),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          const begin = Offset(1.0, 0.0);
          const end = Offset.zero;
          const curve = Curves.easeInOutCubic;

          var tween =
              Tween(begin: begin, end: end).chain(CurveTween(curve: curve));
          var offsetAnimation = animation.drive(tween);

          return SlideTransition(
            position: offsetAnimation,
            child: FadeTransition(
              opacity: animation,
              child: child,
            ),
          );
        },
        transitionDuration: const Duration(milliseconds: 800),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        title: AnimatedBuilder(
          animation: _animationController,
          builder: (context, child) {
            return Opacity(
              opacity: _fadeAnimation.value,
              child: const Text(
                "Language Selection",
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
            );
          },
        ),
        elevation: 0,
      ),
      body: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header with animation
                Transform.translate(
                  offset: Offset(0, 20 * (1 - _fadeAnimation.value)),
                  child: Opacity(
                    opacity: _fadeAnimation.value,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          "Choose your language",
                          style: TextStyle(
                            color: MyColors.cBackgroundColor,
                            fontSize: 25,
                            fontWeight: FontWeight.w800,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          "Select your preferred language to continue",
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 16,
                          ),
                        ),
                        const SizedBox(height: 30),
                      ],
                    ),
                  ),
                ),

                // Language list with staggered animation
                Expanded(
                  child: ListView.builder(
                    itemCount: languages.length,
                    itemBuilder: (context, index) {
                      // Calculate delay for staggered animation
                      final delay = 0.2 + (index * 0.1);
                      final startAnimation = _animationController.value > delay;
                      final animationValue = startAnimation
                          ? (_animationController.value - delay) / (1 - delay)
                          : 0.0;

                      return Transform.translate(
                        offset: Offset(100 * (1 - animationValue), 0),
                        child: Opacity(
                          opacity: animationValue,
                          child: Card(
                            elevation: 2,
                            margin: const EdgeInsets.symmetric(vertical: 8),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                              side: BorderSide(
                                color: index == selectedLanguageIndex
                                    ? MyColors.cGreenColor
                                    : Colors.transparent,
                                width: 2,
                              ),
                            ),
                            child: InkWell(
                              borderRadius: BorderRadius.circular(12),
                              onTap: () {
                                setState(() {
                                  selectedLanguageIndex = index;
                                });

                                // Add a small delay before navigation
                                Future.delayed(
                                    const Duration(milliseconds: 300), () {
                                  _navigateToOnboarding();
                                });
                              },
                              child: Padding(
                                padding: const EdgeInsets.all(16.0),
                                child: Row(
                                  children: [
                                    // Language flag
                                    Container(
                                      width: 40,
                                      height: 40,
                                      alignment: Alignment.center,
                                      decoration: BoxDecoration(
                                        color: Colors.grey[100],
                                        borderRadius: BorderRadius.circular(20),
                                      ),
                                      child: Text(
                                        languages[index]["flag"],
                                        style: const TextStyle(fontSize: 24),
                                      ),
                                    ),
                                    const SizedBox(width: 16),

                                    // Language name
                                    Text(
                                      languages[index]["name"],
                                      style: TextStyle(
                                        fontSize: 18,
                                        fontWeight:
                                            index == selectedLanguageIndex
                                                ? FontWeight.bold
                                                : FontWeight.normal,
                                        color: MyColors.cBackgroundColor,
                                      ),
                                    ),

                                    const Spacer(),

                                    // Selection indicator
                                    if (index == selectedLanguageIndex)
                                      Container(
                                        width: 24,
                                        height: 24,
                                        decoration: BoxDecoration(
                                          color: MyColors.cGreenColor,
                                          borderRadius:
                                              BorderRadius.circular(12),
                                        ),
                                        child: const Icon(
                                          Icons.check,
                                          color: Colors.white,
                                          size: 16,
                                        ),
                                      ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ),

                // Continue button
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 20),
                  child: Opacity(
                    opacity: _fadeAnimation.value,
                    child: SizedBox(
                      width: double.infinity,
                      height: 50,
                      child: ElevatedButton(
                        onPressed: _navigateToOnboarding,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: MyColors.cGreenColor,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: const Text(
                          "Continue",
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
