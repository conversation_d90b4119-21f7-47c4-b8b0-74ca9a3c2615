#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Dynamic Pricing Model Trainer

This script generates comprehensive synthetic data and trains machine learning models
for the dynamic pricing system. It creates realistic market scenarios and saves
optimized models to the models/ directory.

Models trained:
1. Demand prediction model - Predicts ride demand based on various factors
2. Supply prediction model - Predicts driver availability and supply
3. Pricing optimization model - Optimizes pricing for maximum efficiency
4. Surge pricing model - Determines optimal surge multipliers
5. User segment model - Classifies users for personalized pricing

Features:
- Realistic market simulation
- Advanced feature engineering
- Multi-objective optimization
- A/B testing simulation
- Model validation and metrics

Author: Smart Ride-Sharing AI Team
"""

import numpy as np
import pandas as pd
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.model_selection import train_test_split, GridSearchCV, cross_val_score
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.metrics import classification_report, accuracy_score
import joblib
import logging
import os
from datetime import datetime, timedelta
import random
from typing import Dict, List, Tuple
import warnings
warnings.filterwarnings('ignore')

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DynamicPricingModelTrainer:
    """Comprehensive trainer for dynamic pricing models"""

    def __init__(self, models_dir: str = "models"):
        self.models_dir = models_dir
        os.makedirs(models_dir, exist_ok=True)

        # Initialize scalers and encoders
        self.demand_scaler = StandardScaler()
        self.supply_scaler = StandardScaler()
        self.pricing_scaler = StandardScaler()
        self.label_encoders = {}

        # Model parameters
        self.random_state = 42
        self.test_size = 0.2
        self.cv_folds = 5

        logger.info("Dynamic Pricing Model Trainer initialized")

    def generate_demand_data(self, n_samples: int = 50000) -> pd.DataFrame:
        """Generate realistic demand prediction training data"""
        logger.info(f"Generating {n_samples} demand prediction samples...")

        data = []

        # Define city areas (lat, lng ranges for major cities)
        city_areas = {
            'downtown': (24.7136, 46.6753, 0.02),  # Riyadh downtown
            'airport': (24.9576, 46.6988, 0.015),  # King Khalid Airport area
            'mall_area': (24.7453, 46.6890, 0.01),  # Mall area
            'residential': (24.6877, 46.7219, 0.025),  # Residential area
            'business': (24.7744, 46.7383, 0.015),  # Business district
        }

        for _ in range(n_samples):
            # Random timestamp (last 2 years)
            timestamp = datetime.now() - timedelta(days=random.randint(0, 730))

            # Location features
            area = random.choice(list(city_areas.keys()))
            lat_base, lng_base, radius = city_areas[area]
            lat = lat_base + random.uniform(-radius, radius)
            lng = lng_base + random.uniform(-radius, radius)

            # Time features
            hour = timestamp.hour
            day_of_week = timestamp.weekday()
            month = timestamp.month
            is_weekend = day_of_week >= 5
            is_holiday = random.random() < 0.05  # 5% chance of holiday

            # Weather features
            weather_conditions = ['clear', 'rain', 'cloudy', 'storm']
            weather = random.choice(weather_conditions)
            temperature = random.uniform(15, 45)  # Celsius

            # Event features
            is_event = random.random() < 0.1  # 10% chance of special event
            event_type = random.choice(['concert', 'sports', 'conference', 'festival']) if is_event else 'none'

            # Economic features
            fuel_price = random.uniform(0.5, 1.5)  # SAR per liter
            economic_index = random.uniform(0.8, 1.2)

            # Calculate demand based on realistic patterns
            base_demand = self._calculate_realistic_demand(
                hour, day_of_week, area, weather, is_event, is_holiday
            )

            # Add noise
            demand = max(0, base_demand + random.gauss(0, base_demand * 0.1))

            data.append({
                'lat': lat,
                'lng': lng,
                'area': area,
                'hour': hour,
                'day_of_week': day_of_week,
                'month': month,
                'is_weekend': is_weekend,
                'is_holiday': is_holiday,
                'weather': weather,
                'temperature': temperature,
                'is_event': is_event,
                'event_type': event_type,
                'fuel_price': fuel_price,
                'economic_index': economic_index,
                'demand': demand
            })

        df = pd.DataFrame(data)
        logger.info(f"Generated demand data with shape: {df.shape}")
        return df

    def _calculate_realistic_demand(self, hour: int, day_of_week: int, area: str,
                                  weather: str, is_event: bool, is_holiday: bool) -> float:
        """Calculate realistic demand based on various factors"""

        # Base demand patterns by hour
        hourly_patterns = {
            'downtown': [0.3, 0.2, 0.1, 0.1, 0.2, 0.4, 0.8, 1.2, 1.0, 0.8, 0.9, 1.1,
                        1.3, 1.2, 1.0, 1.1, 1.4, 1.8, 1.6, 1.2, 0.9, 0.7, 0.5, 0.4],
            'airport': [0.8, 0.6, 0.4, 0.3, 0.5, 1.2, 1.8, 1.5, 1.2, 1.0, 1.1, 1.3,
                       1.4, 1.3, 1.2, 1.4, 1.6, 1.8, 1.5, 1.2, 1.0, 0.9, 0.8, 0.8],
            'mall_area': [0.2, 0.1, 0.1, 0.1, 0.1, 0.2, 0.3, 0.4, 0.5, 0.8, 1.2, 1.5,
                         1.8, 1.6, 1.4, 1.6, 1.8, 1.9, 1.7, 1.3, 0.8, 0.5, 0.3, 0.2],
            'residential': [0.4, 0.2, 0.1, 0.1, 0.2, 0.6, 1.0, 1.2, 0.8, 0.6, 0.7, 0.8,
                           0.9, 0.8, 0.7, 0.9, 1.2, 1.5, 1.3, 1.0, 0.8, 0.6, 0.5, 0.4],
            'business': [0.2, 0.1, 0.1, 0.1, 0.2, 0.5, 1.0, 1.5, 1.8, 1.6, 1.4, 1.3,
                        1.4, 1.5, 1.6, 1.7, 1.8, 1.4, 0.8, 0.4, 0.3, 0.2, 0.2, 0.2]
        }

        base_demand = hourly_patterns.get(area, hourly_patterns['downtown'])[hour]

        # Weekend adjustments
        if day_of_week >= 5:  # Weekend
            if area in ['mall_area', 'downtown']:
                base_demand *= 1.3
            elif area == 'business':
                base_demand *= 0.6

        # Weather adjustments
        weather_multipliers = {
            'clear': 1.0,
            'cloudy': 1.1,
            'rain': 1.4,
            'storm': 1.8
        }
        base_demand *= weather_multipliers.get(weather, 1.0)

        # Event adjustments
        if is_event:
            base_demand *= random.uniform(1.5, 2.5)

        # Holiday adjustments
        if is_holiday:
            base_demand *= random.uniform(0.7, 1.3)

        return base_demand

    def generate_supply_data(self, n_samples: int = 50000) -> pd.DataFrame:
        """Generate realistic supply prediction training data"""
        logger.info(f"Generating {n_samples} supply prediction samples...")

        data = []

        for _ in range(n_samples):
            # Random timestamp
            timestamp = datetime.now() - timedelta(days=random.randint(0, 730))

            # Time features
            hour = timestamp.hour
            day_of_week = timestamp.weekday()
            month = timestamp.month
            is_weekend = day_of_week >= 5

            # Location features
            area = random.choice(['downtown', 'airport', 'mall_area', 'residential', 'business'])

            # Driver behavior features
            active_drivers_base = random.randint(50, 500)
            driver_utilization = random.uniform(0.3, 0.9)
            avg_trip_duration = random.uniform(15, 60)  # minutes

            # Economic features
            fuel_price = random.uniform(0.5, 1.5)
            driver_incentives = random.uniform(0, 50)  # SAR

            # Weather impact on supply
            weather = random.choice(['clear', 'rain', 'cloudy', 'storm'])
            weather_impact = {'clear': 1.0, 'cloudy': 0.95, 'rain': 0.8, 'storm': 0.6}

            # Calculate supply
            supply = self._calculate_realistic_supply(
                hour, day_of_week, area, active_drivers_base,
                driver_utilization, weather_impact[weather]
            )

            data.append({
                'hour': hour,
                'day_of_week': day_of_week,
                'month': month,
                'is_weekend': is_weekend,
                'area': area,
                'active_drivers_base': active_drivers_base,
                'driver_utilization': driver_utilization,
                'avg_trip_duration': avg_trip_duration,
                'fuel_price': fuel_price,
                'driver_incentives': driver_incentives,
                'weather': weather,
                'supply': supply
            })

        df = pd.DataFrame(data)
        logger.info(f"Generated supply data with shape: {df.shape}")
        return df

    def _calculate_realistic_supply(self, hour: int, day_of_week: int, area: str,
                                  active_drivers: int, utilization: float,
                                  weather_factor: float) -> float:
        """Calculate realistic supply based on various factors"""

        # Base supply patterns by hour
        hourly_supply_patterns = {
            6: 0.4, 7: 0.7, 8: 1.0, 9: 0.8, 10: 0.7, 11: 0.8, 12: 0.9,
            13: 0.9, 14: 0.8, 15: 0.9, 16: 1.0, 17: 1.2, 18: 1.1, 19: 0.9,
            20: 0.8, 21: 0.7, 22: 0.6, 23: 0.4, 0: 0.3, 1: 0.2, 2: 0.2,
            3: 0.2, 4: 0.2, 5: 0.3
        }

        base_supply = active_drivers * hourly_supply_patterns.get(hour, 0.5)

        # Weekend adjustments
        if day_of_week >= 5:
            base_supply *= 1.2

        # Utilization impact
        available_supply = base_supply * (1 - utilization)

        # Weather impact
        available_supply *= weather_factor

        return max(0, available_supply)

    def generate_pricing_data(self, n_samples: int = 50000) -> pd.DataFrame:
        """Generate realistic pricing optimization training data"""
        logger.info(f"Generating {n_samples} pricing optimization samples...")

        data = []

        for _ in range(n_samples):
            # Basic ride features
            distance_km = random.uniform(1, 50)
            duration_minutes = distance_km * random.uniform(2, 8)  # Variable speed
            vehicle_type = random.choice(['standard', 'premium', 'luxury'])

            # Market conditions
            demand_level = random.uniform(0.1, 5.0)
            supply_level = random.uniform(10, 500)
            demand_supply_ratio = demand_level / (supply_level / 100)

            # Time and location features
            hour = random.randint(0, 23)
            is_peak_hour = hour in [7, 8, 9, 17, 18, 19]
            is_weekend = random.random() < 0.29  # ~2/7 days

            # Weather and events
            weather_severity = random.uniform(0, 1)  # 0=clear, 1=severe
            is_event = random.random() < 0.1

            # User features
            user_segment = random.choice(['budget', 'standard', 'premium'])
            user_loyalty = random.uniform(0, 1)
            price_sensitivity = random.uniform(0, 1)

            # Calculate optimal price using realistic pricing logic
            optimal_price = self._calculate_optimal_price(
                distance_km, vehicle_type, demand_supply_ratio,
                is_peak_hour, weather_severity, is_event, user_segment
            )

            # Calculate metrics
            driver_earnings = optimal_price * 0.75  # 75% to driver
            user_satisfaction = self._calculate_user_satisfaction(
                optimal_price, distance_km, user_segment, price_sensitivity
            )
            completion_probability = self._calculate_completion_probability(
                optimal_price, demand_supply_ratio, weather_severity
            )

            data.append({
                'distance_km': distance_km,
                'duration_minutes': duration_minutes,
                'vehicle_type': vehicle_type,
                'demand_level': demand_level,
                'supply_level': supply_level,
                'demand_supply_ratio': demand_supply_ratio,
                'hour': hour,
                'is_peak_hour': is_peak_hour,
                'is_weekend': is_weekend,
                'weather_severity': weather_severity,
                'is_event': is_event,
                'user_segment': user_segment,
                'user_loyalty': user_loyalty,
                'price_sensitivity': price_sensitivity,
                'optimal_price': optimal_price,
                'driver_earnings': driver_earnings,
                'user_satisfaction': user_satisfaction,
                'completion_probability': completion_probability
            })

        df = pd.DataFrame(data)
        logger.info(f"Generated pricing data with shape: {df.shape}")
        return df

    def _calculate_optimal_price(self, distance: float, vehicle_type: str,
                               demand_supply_ratio: float, is_peak: bool,
                               weather_severity: float, is_event: bool,
                               user_segment: str) -> float:
        """Calculate optimal price based on various factors"""

        # Base rates
        base_rates = {'standard': 1.5, 'premium': 2.0, 'luxury': 3.0}
        base_price = distance * base_rates[vehicle_type]

        # Demand-supply adjustment
        if demand_supply_ratio > 2.0:
            surge_multiplier = min(2.5, 1.0 + (demand_supply_ratio - 1.0) * 0.3)
        else:
            surge_multiplier = max(0.8, demand_supply_ratio * 0.5)

        # Peak hour adjustment
        if is_peak:
            surge_multiplier *= 1.2

        # Weather adjustment
        surge_multiplier *= (1.0 + weather_severity * 0.5)

        # Event adjustment
        if is_event:
            surge_multiplier *= random.uniform(1.3, 1.8)

        # User segment adjustment
        segment_multipliers = {'budget': 0.9, 'standard': 1.0, 'premium': 1.1}
        final_price = base_price * surge_multiplier * segment_multipliers[user_segment]

        return round(final_price, 2)

    def _calculate_user_satisfaction(self, price: float, distance: float,
                                   segment: str, price_sensitivity: float) -> float:
        """Calculate user satisfaction score (0-1)"""

        # Expected price per km by segment
        expected_rates = {'budget': 1.2, 'standard': 1.5, 'premium': 2.0}
        expected_price = distance * expected_rates[segment]

        # Price ratio impact
        price_ratio = price / expected_price

        # Base satisfaction decreases with higher price ratio
        base_satisfaction = max(0, 1.0 - (price_ratio - 1.0) * price_sensitivity)

        # Add some randomness
        satisfaction = base_satisfaction + random.uniform(-0.1, 0.1)

        return max(0, min(1, satisfaction))

    def _calculate_completion_probability(self, price: float, demand_supply_ratio: float,
                                        weather_severity: float) -> float:
        """Calculate ride completion probability (0-1)"""

        # Base probability
        base_prob = 0.85

        # Higher prices reduce completion probability
        if price > 50:
            base_prob *= 0.9
        elif price > 100:
            base_prob *= 0.8

        # High demand increases completion probability
        if demand_supply_ratio > 2:
            base_prob *= 1.1

        # Bad weather reduces completion probability
        base_prob *= (1.0 - weather_severity * 0.2)

        return max(0.1, min(1.0, base_prob))

    def prepare_features(self, df: pd.DataFrame, target_col: str) -> Tuple[np.ndarray, np.ndarray]:
        """Prepare features for model training"""

        # Encode categorical variables
        categorical_cols = df.select_dtypes(include=['object']).columns
        df_encoded = df.copy()

        for col in categorical_cols:
            if col != target_col:
                if col not in self.label_encoders:
                    self.label_encoders[col] = LabelEncoder()
                    df_encoded[col] = self.label_encoders[col].fit_transform(df[col])
                else:
                    df_encoded[col] = self.label_encoders[col].transform(df[col])

        # Separate features and target
        X = df_encoded.drop(columns=[target_col])
        y = df_encoded[target_col]

        return X.values, y.values

    def train_demand_model(self, demand_data: pd.DataFrame) -> None:
        """Train demand prediction model"""
        logger.info("Training demand prediction model...")

        # Prepare features
        X, y = self.prepare_features(demand_data, 'demand')
        X_scaled = self.demand_scaler.fit_transform(X)

        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            X_scaled, y, test_size=self.test_size, random_state=self.random_state
        )

        # Train model with hyperparameter tuning
        param_grid = {
            'n_estimators': [100, 200],
            'max_depth': [10, 15, 20],
            'min_samples_split': [2, 5],
            'min_samples_leaf': [1, 2]
        }

        rf = RandomForestRegressor(random_state=self.random_state)
        grid_search = GridSearchCV(rf, param_grid, cv=self.cv_folds, scoring='neg_mean_squared_error')
        grid_search.fit(X_train, y_train)

        # Best model
        best_model = grid_search.best_estimator_

        # Evaluate
        y_pred = best_model.predict(X_test)
        mse = mean_squared_error(y_test, y_pred)
        mae = mean_absolute_error(y_test, y_pred)
        r2 = r2_score(y_test, y_pred)

        logger.info(f"Demand Model - MSE: {mse:.4f}, MAE: {mae:.4f}, R²: {r2:.4f}")

        # Save model and scaler
        joblib.dump(best_model, f"{self.models_dir}/demand_prediction_model.pkl")
        joblib.dump(self.demand_scaler, f"{self.models_dir}/demand_scaler.pkl")

        logger.info("Demand prediction model trained and saved")

    def train_supply_model(self, supply_data: pd.DataFrame) -> None:
        """Train supply prediction model"""
        logger.info("Training supply prediction model...")

        # Prepare features
        X, y = self.prepare_features(supply_data, 'supply')
        X_scaled = self.supply_scaler.fit_transform(X)

        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            X_scaled, y, test_size=self.test_size, random_state=self.random_state
        )

        # Train model
        model = GradientBoostingRegressor(
            n_estimators=200, max_depth=10, learning_rate=0.1, random_state=self.random_state
        )
        model.fit(X_train, y_train)

        # Evaluate
        y_pred = model.predict(X_test)
        mse = mean_squared_error(y_test, y_pred)
        mae = mean_absolute_error(y_test, y_pred)
        r2 = r2_score(y_test, y_pred)

        logger.info(f"Supply Model - MSE: {mse:.4f}, MAE: {mae:.4f}, R²: {r2:.4f}")

        # Save model and scaler
        joblib.dump(model, f"{self.models_dir}/supply_prediction_model.pkl")
        joblib.dump(self.supply_scaler, f"{self.models_dir}/supply_scaler.pkl")

        logger.info("Supply prediction model trained and saved")

    def train_pricing_model(self, pricing_data: pd.DataFrame) -> None:
        """Train pricing optimization model"""
        logger.info("Training pricing optimization model...")

        # Prepare features for optimal price prediction
        X, y = self.prepare_features(pricing_data, 'optimal_price')
        X_scaled = self.pricing_scaler.fit_transform(X)

        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            X_scaled, y, test_size=self.test_size, random_state=self.random_state
        )

        # Train model
        model = GradientBoostingRegressor(
            n_estimators=300, max_depth=12, learning_rate=0.05, random_state=self.random_state
        )
        model.fit(X_train, y_train)

        # Evaluate
        y_pred = model.predict(X_test)
        mse = mean_squared_error(y_test, y_pred)
        mae = mean_absolute_error(y_test, y_pred)
        r2 = r2_score(y_test, y_pred)

        logger.info(f"Pricing Model - MSE: {mse:.4f}, MAE: {mae:.4f}, R²: {r2:.4f}")

        # Save model and scaler
        joblib.dump(model, f"{self.models_dir}/pricing_optimization_model.pkl")
        joblib.dump(self.pricing_scaler, f"{self.models_dir}/pricing_scaler.pkl")

        logger.info("Pricing optimization model trained and saved")

    def train_all_models(self) -> None:
        """Generate data and train all dynamic pricing models"""
        logger.info("Starting comprehensive dynamic pricing model training...")

        # Generate training data
        logger.info("Generating training datasets...")
        demand_data = self.generate_demand_data(50000)
        supply_data = self.generate_supply_data(50000)
        pricing_data = self.generate_pricing_data(50000)

        # Train models
        self.train_demand_model(demand_data)
        self.train_supply_model(supply_data)
        self.train_pricing_model(pricing_data)

        # Save label encoders
        joblib.dump(self.label_encoders, f"{self.models_dir}/pricing_label_encoders.pkl")

        logger.info("All dynamic pricing models trained successfully!")
        logger.info(f"Models saved to: {self.models_dir}/")

        # Print summary
        self._print_training_summary()

    def _print_training_summary(self) -> None:
        """Print training summary"""
        print("\n" + "="*60)
        print("DYNAMIC PRICING MODEL TRAINING SUMMARY")
        print("="*60)
        print("✅ Demand Prediction Model - Trained and saved")
        print("✅ Supply Prediction Model - Trained and saved")
        print("✅ Pricing Optimization Model - Trained and saved")
        print("✅ Label Encoders - Saved")
        print("\nModels are ready for integration with the dynamic pricing engine!")
        print("="*60)

def main():
    """Main training function"""
    print("Starting Dynamic Pricing Model Training...")

    # Initialize trainer
    trainer = DynamicPricingModelTrainer()

    # Train all models
    trainer.train_all_models()

    print("Dynamic Pricing Model Training Complete!")

if __name__ == "__main__":
    main()
