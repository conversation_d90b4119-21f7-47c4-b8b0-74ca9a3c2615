import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import '../providers/ai_ride_provider.dart';
import '../models/ride_request.dart';
import '../models/driver_model.dart';
import '../theme/app_colors.dart';
import 'ai_ride_matching_widget.dart';
import 'ai_dynamic_pricing_widget.dart';

/// AI-Enhanced Ride Booking Widget that integrates all AI features
class AIEnhancedRideBooking extends StatefulWidget {
  final LatLng? pickupLocation;
  final LatLng? dropoffLocation;
  final Function(Map<String, dynamic>)? onRideBooked;
  final List<DriverModel>? availableDrivers;

  const AIEnhancedRideBooking({
    Key? key,
    this.pickupLocation,
    this.dropoffLocation,
    this.onRideBooked,
    this.availableDrivers,
  }) : super(key: key);

  @override
  State<AIEnhancedRideBooking> createState() => _AIEnhancedRideBookingState();
}

class _AIEnhancedRideBookingState extends State<AIEnhancedRideBooking>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  RideRequest? _currentRideRequest;
  Map<String, dynamic>? _selectedDriver;
  Map<String, dynamic>? _calculatedPricing;
  bool _isProcessingSmartRequest = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _initializeAIService();
    _createRideRequest();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _initializeAIService() async {
    final aiProvider = Provider.of<AIRideProvider>(context, listen: false);
    await aiProvider.initializeAIService();
  }

  void _createRideRequest() {
    if (widget.pickupLocation != null && widget.dropoffLocation != null) {
      _currentRideRequest = RideRequest(
        id: 'req_${DateTime.now().millisecondsSinceEpoch}',
        riderId: 'current_user', // You should get this from auth service
        pickupLocation: {
          'latitude': widget.pickupLocation?.latitude ?? 0.0,
          'longitude': widget.pickupLocation?.longitude ?? 0.0,
        },
        destination: {
          'latitude': widget.dropoffLocation?.latitude ?? 0.0,
          'longitude': widget.dropoffLocation?.longitude ?? 0.0,
        },
        status: 'pending',
        timestamp: DateTime.now(),
        passengerCount: 1,
        vehicleType: 'standard',
        estimatedDistance: _calculateDistance(),
        estimatedDuration: _calculateDuration(),
        urgencyLevel: 'normal',
      );
    }
  }

  double _calculateDistance() {
    if (widget.pickupLocation == null || widget.dropoffLocation == null) {
      return 5.0; // Default distance
    }
    // Simple distance calculation (in real app, use proper distance calculation)
    final lat1 = widget.pickupLocation!.latitude;
    final lon1 = widget.pickupLocation!.longitude;
    final lat2 = widget.dropoffLocation!.latitude;
    final lon2 = widget.dropoffLocation!.longitude;

    final distance = ((lat2 - lat1).abs() + (lon2 - lon1).abs()) *
        111; // Rough km calculation
    return distance.clamp(1.0, 50.0);
  }

  int _calculateDuration() {
    final distance = _calculateDistance();
    return (distance * 2.5).round(); // Rough duration calculation
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AIRideProvider>(
      builder: (context, aiProvider, child) {
        return Container(
          height: MediaQuery.of(context).size.height * 0.8,
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Column(
            children: [
              _buildHeader(aiProvider),
              _buildTabBar(),
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    _buildSmartBookingTab(aiProvider),
                    _buildDriverMatchingTab(aiProvider),
                    _buildPricingTab(aiProvider),
                  ],
                ),
              ),
              _buildActionButtons(aiProvider),
            ],
          ),
        );
      },
    );
  }

  Widget _buildHeader(AIRideProvider aiProvider) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.topRight,
          colors: [
            AppColors.primary,
            AppColors.secondary,
          ],
        ),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.5),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 16),

          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.psychology,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'AI-Powered Ride Booking',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      aiProvider.aiServiceConnected
                          ? 'Smart algorithms optimizing your ride'
                          : 'Standard booking (AI offline)',
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.8),
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: aiProvider.aiServiceConnected
                      ? Colors.green.withOpacity(0.2)
                      : Colors.orange.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: aiProvider.aiServiceConnected
                        ? Colors.green
                        : Colors.orange,
                    width: 1,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      aiProvider.aiServiceConnected
                          ? Icons.check_circle
                          : Icons.warning,
                      size: 12,
                      color: aiProvider.aiServiceConnected
                          ? Colors.green
                          : Colors.orange,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      aiProvider.aiServiceConnected ? 'AI ON' : 'AI OFF',
                      style: TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                        color: aiProvider.aiServiceConnected
                            ? Colors.green
                            : Colors.orange,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey[50],
        border: Border(
          bottom: BorderSide(color: Colors.grey[200]!, width: 1),
        ),
      ),
      child: TabBar(
        controller: _tabController,
        labelColor: AppColors.primary,
        unselectedLabelColor: Colors.grey[600],
        indicatorColor: AppColors.primary,
        indicatorWeight: 3,
        tabs: const [
          Tab(
            icon: Icon(Icons.auto_awesome, size: 20),
            text: 'Smart Booking',
          ),
          Tab(
            icon: Icon(Icons.people, size: 20),
            text: 'Driver Match',
          ),
          Tab(
            icon: Icon(Icons.attach_money, size: 20),
            text: 'AI Pricing',
          ),
        ],
      ),
    );
  }

  Widget _buildSmartBookingTab(AIRideProvider aiProvider) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSmartBookingCard(aiProvider),
          const SizedBox(height: 16),
          _buildRideDetailsCard(),
          const SizedBox(height: 16),
          _buildAIInsightsCard(aiProvider),
        ],
      ),
    );
  }

  Widget _buildSmartBookingCard(AIRideProvider aiProvider) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.auto_awesome, color: AppColors.primary),
                const SizedBox(width: 8),
                const Text(
                  'Smart Ride Processing',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            if (_isProcessingSmartRequest) ...[
              const LinearProgressIndicator(),
              const SizedBox(height: 8),
              const Text('AI is optimizing your ride request...'),
            ] else ...[
              ElevatedButton.icon(
                onPressed: _currentRideRequest != null
                    ? () => _processSmartRideRequest(aiProvider)
                    : null,
                icon: const Icon(Icons.psychology),
                label: const Text('Process with AI'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                  minimumSize: const Size(double.infinity, 48),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildDriverMatchingTab(AIRideProvider aiProvider) {
    if (_currentRideRequest == null) {
      return const Center(
        child: Text('Please set pickup and dropoff locations first'),
      );
    }

    return AIRideMatchingWidget(
      rideRequest: _currentRideRequest!,
      availableDrivers: widget.availableDrivers ?? [],
      onDriverSelected: (driver) {
        setState(() {
          _selectedDriver = driver;
        });
        _tabController.animateTo(2); // Move to pricing tab
      },
    );
  }

  Widget _buildPricingTab(AIRideProvider aiProvider) {
    if (_currentRideRequest == null) {
      return const Center(
        child: Text('Please set pickup and dropoff locations first'),
      );
    }

    return AIDynamicPricingWidget(
      rideRequest: _currentRideRequest!,
      onPriceCalculated: (pricing) {
        setState(() {
          _calculatedPricing = pricing;
        });
      },
    );
  }

  Widget _buildRideDetailsCard() {
    if (_currentRideRequest == null) return const SizedBox.shrink();

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Ride Details',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            _buildDetailRow(
              Icons.straighten,
              'Distance',
              '${_currentRideRequest!.estimatedDistance?.toStringAsFixed(1) ?? 'N/A'} km',
            ),
            _buildDetailRow(
              Icons.access_time,
              'Duration',
              '${_currentRideRequest!.estimatedDuration ?? 'N/A'} min',
            ),
            _buildDetailRow(
              Icons.directions_car,
              'Vehicle Type',
              _currentRideRequest!.vehicleType ?? 'Standard',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(IconData icon, String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(icon, size: 16, color: Colors.grey[600]),
          const SizedBox(width: 8),
          Text(
            '$label:',
            style: TextStyle(color: Colors.grey[600]),
          ),
          const Spacer(),
          Text(
            value,
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
        ],
      ),
    );
  }

  Widget _buildAIInsightsCard(AIRideProvider aiProvider) {
    final serviceInfo = aiProvider.getAIServiceInfo();

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.insights, color: AppColors.primary),
                const SizedBox(width: 8),
                const Text(
                  'AI Insights',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            if (serviceInfo['connected']) ...[
              ...serviceInfo['features_available']
                  .map<Widget>(
                    (feature) => Padding(
                      padding: const EdgeInsets.symmetric(vertical: 2),
                      child: Row(
                        children: [
                          const Icon(
                            Icons.check_circle,
                            size: 16,
                            color: Colors.green,
                          ),
                          const SizedBox(width: 8),
                          Text(feature, style: const TextStyle(fontSize: 12)),
                        ],
                      ),
                    ),
                  )
                  .toList(),
            ] else ...[
              Row(
                children: [
                  const Icon(
                    Icons.warning,
                    size: 16,
                    color: Colors.orange,
                  ),
                  const SizedBox(width: 8),
                  const Text(
                    'AI service offline - using standard features',
                    style: TextStyle(fontSize: 12),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons(AIRideProvider aiProvider) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        border: Border(
          top: BorderSide(color: Colors.grey[200]!, width: 1),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            flex: 2,
            child: ElevatedButton(
              onPressed: _canBookRide() ? _bookRide : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                minimumSize: const Size(0, 48),
              ),
              child: Text(
                _selectedDriver != null ? 'Book Ride' : 'Select Driver First',
              ),
            ),
          ),
        ],
      ),
    );
  }

  bool _canBookRide() {
    return _currentRideRequest != null &&
        _selectedDriver != null &&
        !_isProcessingSmartRequest;
  }

  Future<void> _processSmartRideRequest(AIRideProvider aiProvider) async {
    if (_currentRideRequest == null) return;

    setState(() {
      _isProcessingSmartRequest = true;
    });

    try {
      final result = await aiProvider.processSmartRideRequest(
        rideRequest: _currentRideRequest!,
        availableDrivers: widget.availableDrivers ?? [],
      );

      if (result != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('AI has optimized your ride request!'),
            backgroundColor: Colors.green,
          ),
        );

        // Auto-select best match if available
        if (aiProvider.aiMatches.isNotEmpty) {
          setState(() {
            _selectedDriver = aiProvider.aiMatches.first;
          });
        }
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('AI processing failed: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isProcessingSmartRequest = false;
      });
    }
  }

  void _bookRide() {
    if (_selectedDriver == null || _currentRideRequest == null) return;

    final rideData = {
      'ride_request': _currentRideRequest!.toJson(),
      'selected_driver': _selectedDriver,
      'pricing': _calculatedPricing,
      'ai_powered': true,
    };

    if (widget.onRideBooked != null) {
      widget.onRideBooked!(rideData);
    }

    Navigator.pop(context);

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Ride booked successfully with AI optimization!'),
        backgroundColor: Colors.green,
      ),
    );
  }
}
