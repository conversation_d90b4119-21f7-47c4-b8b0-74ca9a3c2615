import 'package:geolocator/geolocator.dart';

/// Enhanced Ride Request Model
class RideRequest {
  final String id;
  final String riderId;
  final Map<String, dynamic> pickupLocation;
  final Map<String, dynamic> destination;
  final String status;
  final DateTime timestamp;
  final String? driverId;
  final Map<String, dynamic>? preferences;
  final int priority;
  final String? traceId;

  // Additional properties for AI integration
  final int? passengerCount;
  final String? vehicleType;
  final double? estimatedDistance;
  final int? estimatedDuration;
  final String? urgencyLevel;

  RideRequest({
    required this.id,
    required this.riderId,
    required this.pickupLocation,
    required this.destination,
    required this.status,
    required this.timestamp,
    this.driverId,
    this.preferences,
    this.priority = 1,
    this.traceId,
    this.passengerCount,
    this.vehicleType,
    this.estimatedDistance,
    this.estimatedDuration,
    this.urgencyLevel,
  });

  factory RideRequest.fromJson(Map<String, dynamic> json) {
    return RideRequest(
      id: json['id'] ?? '',
      riderId: json['riderId'] ?? '',
      pickupLocation: Map<String, dynamic>.from(json['pickupLocation'] ?? {}),
      destination: Map<String, dynamic>.from(json['destination'] ?? {}),
      status: json['status'] ?? 'pending',
      timestamp: json['timestamp'] != null
          ? DateTime.parse(json['timestamp'])
          : DateTime.now(),
      driverId: json['driverId'],
      preferences: json['preferences'] != null
          ? Map<String, dynamic>.from(json['preferences'])
          : null,
      priority: json['priority'] ?? 1,
      traceId: json['traceId'],
      passengerCount: json['passengerCount'],
      vehicleType: json['vehicleType'],
      estimatedDistance: json['estimatedDistance']?.toDouble(),
      estimatedDuration: json['estimatedDuration'],
      urgencyLevel: json['urgencyLevel'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'riderId': riderId,
      'pickupLocation': pickupLocation,
      'destination': destination,
      'status': status,
      'timestamp': timestamp.toIso8601String(),
      'driverId': driverId,
      'preferences': preferences,
      'priority': priority,
      'traceId': traceId,
      'passengerCount': passengerCount,
      'vehicleType': vehicleType,
      'estimatedDistance': estimatedDistance,
      'estimatedDuration': estimatedDuration,
      'urgencyLevel': urgencyLevel,
    };
  }

  RideRequest copyWith({
    String? id,
    String? riderId,
    Map<String, dynamic>? pickupLocation,
    Map<String, dynamic>? destination,
    String? status,
    DateTime? timestamp,
    String? driverId,
    Map<String, dynamic>? preferences,
    int? priority,
    String? traceId,
    int? passengerCount,
    String? vehicleType,
    double? estimatedDistance,
    int? estimatedDuration,
    String? urgencyLevel,
  }) {
    return RideRequest(
      id: id ?? this.id,
      riderId: riderId ?? this.riderId,
      pickupLocation: pickupLocation ?? this.pickupLocation,
      destination: destination ?? this.destination,
      status: status ?? this.status,
      timestamp: timestamp ?? this.timestamp,
      driverId: driverId ?? this.driverId,
      preferences: preferences ?? this.preferences,
      priority: priority ?? this.priority,
      traceId: traceId ?? this.traceId,
      passengerCount: passengerCount ?? this.passengerCount,
      vehicleType: vehicleType ?? this.vehicleType,
      estimatedDistance: estimatedDistance ?? this.estimatedDistance,
      estimatedDuration: estimatedDuration ?? this.estimatedDuration,
      urgencyLevel: urgencyLevel ?? this.urgencyLevel,
    );
  }

  @override
  String toString() {
    return 'RideRequest(id: $id, riderId: $riderId, status: $status, timestamp: $timestamp)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is RideRequest && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// Enhanced Ride Request Status
enum RideRequestStatus {
  pending,
  accepted,
  inProgress,
  completed,
  cancelled,
  timeout,
}

/// Ride Request Priority Levels
enum RideRequestPriority {
  low(1),
  normal(2),
  high(3),
  urgent(4),
  emergency(5);

  const RideRequestPriority(this.value);
  final int value;
}

/// Location Model for Ride Requests
class RideLocation {
  final double latitude;
  final double longitude;
  final String? address;
  final DateTime timestamp;
  final double? accuracy;

  RideLocation({
    required this.latitude,
    required this.longitude,
    this.address,
    required this.timestamp,
    this.accuracy,
  });

  factory RideLocation.fromPosition(Position position, {String? address}) {
    return RideLocation(
      latitude: position.latitude,
      longitude: position.longitude,
      address: address,
      timestamp: position.timestamp,
      accuracy: position.accuracy,
    );
  }

  factory RideLocation.fromJson(Map<String, dynamic> json) {
    return RideLocation(
      latitude: json['latitude']?.toDouble() ?? 0.0,
      longitude: json['longitude']?.toDouble() ?? 0.0,
      address: json['address'],
      timestamp: json['timestamp'] != null
          ? DateTime.parse(json['timestamp'])
          : DateTime.now(),
      accuracy: json['accuracy']?.toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'latitude': latitude,
      'longitude': longitude,
      'address': address,
      'timestamp': timestamp.toIso8601String(),
      'accuracy': accuracy,
    };
  }

  @override
  String toString() {
    return 'RideLocation(lat: $latitude, lng: $longitude, address: $address)';
  }
}

/// Driver Information for Ride Requests
class DriverInfo {
  final String id;
  final String name;
  final String? phoneNumber;
  final double rating;
  final String vehicleType;
  final String? vehiclePlate;
  final String? vehicleColor;
  final String? profileImage;
  final RideLocation? currentLocation;

  DriverInfo({
    required this.id,
    required this.name,
    this.phoneNumber,
    required this.rating,
    required this.vehicleType,
    this.vehiclePlate,
    this.vehicleColor,
    this.profileImage,
    this.currentLocation,
  });

  factory DriverInfo.fromJson(Map<String, dynamic> json) {
    return DriverInfo(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      phoneNumber: json['phoneNumber'],
      rating: json['rating']?.toDouble() ?? 0.0,
      vehicleType: json['vehicleType'] ?? '',
      vehiclePlate: json['vehiclePlate'],
      vehicleColor: json['vehicleColor'],
      profileImage: json['profileImage'],
      currentLocation: json['currentLocation'] != null
          ? RideLocation.fromJson(json['currentLocation'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'phoneNumber': phoneNumber,
      'rating': rating,
      'vehicleType': vehicleType,
      'vehiclePlate': vehiclePlate,
      'vehicleColor': vehicleColor,
      'profileImage': profileImage,
      'currentLocation': currentLocation?.toJson(),
    };
  }

  @override
  String toString() {
    return 'DriverInfo(id: $id, name: $name, rating: $rating, vehicleType: $vehicleType)';
  }
}

/// Ride Request Metrics for Analytics
class RideRequestMetrics {
  final String requestId;
  final DateTime initiatedAt;
  final DateTime? matchedAt;
  final DateTime? acceptedAt;
  final DateTime? completedAt;
  final Duration? matchTime;
  final Duration? responseTime;
  final Duration? totalTime;
  final double? matchScore;
  final int rejectionCount;
  final List<String> rejectedDrivers;

  RideRequestMetrics({
    required this.requestId,
    required this.initiatedAt,
    this.matchedAt,
    this.acceptedAt,
    this.completedAt,
    this.matchTime,
    this.responseTime,
    this.totalTime,
    this.matchScore,
    this.rejectionCount = 0,
    this.rejectedDrivers = const [],
  });

  factory RideRequestMetrics.fromJson(Map<String, dynamic> json) {
    return RideRequestMetrics(
      requestId: json['requestId'] ?? '',
      initiatedAt: DateTime.parse(json['initiatedAt']),
      matchedAt:
          json['matchedAt'] != null ? DateTime.parse(json['matchedAt']) : null,
      acceptedAt: json['acceptedAt'] != null
          ? DateTime.parse(json['acceptedAt'])
          : null,
      completedAt: json['completedAt'] != null
          ? DateTime.parse(json['completedAt'])
          : null,
      matchTime: json['matchTime'] != null
          ? Duration(milliseconds: json['matchTime'])
          : null,
      responseTime: json['responseTime'] != null
          ? Duration(milliseconds: json['responseTime'])
          : null,
      totalTime: json['totalTime'] != null
          ? Duration(milliseconds: json['totalTime'])
          : null,
      matchScore: json['matchScore']?.toDouble(),
      rejectionCount: json['rejectionCount'] ?? 0,
      rejectedDrivers: List<String>.from(json['rejectedDrivers'] ?? []),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'requestId': requestId,
      'initiatedAt': initiatedAt.toIso8601String(),
      'matchedAt': matchedAt?.toIso8601String(),
      'acceptedAt': acceptedAt?.toIso8601String(),
      'completedAt': completedAt?.toIso8601String(),
      'matchTime': matchTime?.inMilliseconds,
      'responseTime': responseTime?.inMilliseconds,
      'totalTime': totalTime?.inMilliseconds,
      'matchScore': matchScore,
      'rejectionCount': rejectionCount,
      'rejectedDrivers': rejectedDrivers,
    };
  }
}
