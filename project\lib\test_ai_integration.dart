import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'core/providers/ai_ride_provider.dart';
import 'core/widgets/ai_status_indicator.dart';
import 'core/widgets/ai_dynamic_pricing_widget.dart';
import 'core/models/ride_request.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

/// Test screen for AI integration
/// This screen demonstrates all AI features working together
class TestAIIntegrationScreen extends StatefulWidget {
  const TestAIIntegrationScreen({Key? key}) : super(key: key);

  @override
  State<TestAIIntegrationScreen> createState() => _TestAIIntegrationScreenState();
}

class _TestAIIntegrationScreenState extends State<TestAIIntegrationScreen> {
  late RideRequest _testRideRequest;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _initializeTestData();
    _initializeAI();
  }

  void _initializeTestData() {
    // Create a test ride request
    _testRideRequest = RideRequest(
      id: 'test_ride_${DateTime.now().millisecondsSinceEpoch}',
      pickupLocation: const LatLng(24.7136, 46.6753), // Riyadh coordinates
      dropoffLocation: const LatLng(24.7453, 46.6890),
      passengerCount: 1,
      vehicleType: 'standard',
      estimatedDistance: 8.5,
      estimatedDuration: 20,
      urgencyLevel: 'normal',
    );
  }

  Future<void> _initializeAI() async {
    setState(() => _isLoading = true);
    
    try {
      final aiProvider = Provider.of<AIRideProvider>(context, listen: false);
      await aiProvider.initializeAIService();
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              aiProvider.aiServiceConnected 
                ? '✅ AI Service Connected Successfully!'
                : '⚠️ AI Service Offline - Using Fallback Mode'
            ),
            backgroundColor: aiProvider.aiServiceConnected 
              ? Colors.green 
              : Colors.orange,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ AI Initialization Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('AI Integration Test'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          const AIStatusIndicator(showDetails: true),
          const SizedBox(width: 16),
        ],
      ),
      body: _isLoading 
        ? const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text('Initializing AI Services...'),
              ],
            ),
          )
        : Consumer<AIRideProvider>(
            builder: (context, aiProvider, child) {
              return SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildStatusCard(aiProvider),
                    const SizedBox(height: 20),
                    _buildTestActionsCard(aiProvider),
                    const SizedBox(height: 20),
                    _buildPricingTestCard(),
                    const SizedBox(height: 20),
                    _buildResultsCard(aiProvider),
                  ],
                ),
              );
            },
          ),
    );
  }

  Widget _buildStatusCard(AIRideProvider aiProvider) {
    final serviceInfo = aiProvider.getAIServiceInfo();
    
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.psychology,
                  color: aiProvider.aiServiceConnected ? Colors.green : Colors.orange,
                ),
                const SizedBox(width: 8),
                Text(
                  'AI Service Status',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: 12),
            _buildStatusRow(
              'Connection',
              serviceInfo['connected'] ? 'Connected' : 'Disconnected',
              serviceInfo['connected'],
            ),
            const SizedBox(height: 8),
            const Text(
              'Available Features:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 4),
            if (serviceInfo['features_available'].isNotEmpty) ...[
              ...serviceInfo['features_available'].map<Widget>((feature) => 
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 2),
                  child: Row(
                    children: [
                      const Icon(Icons.check_circle, size: 16, color: Colors.green),
                      const SizedBox(width: 8),
                      Text(feature, style: const TextStyle(fontSize: 12)),
                    ],
                  ),
                ),
              ).toList(),
            ] else ...[
              const Row(
                children: [
                  Icon(Icons.warning, size: 16, color: Colors.orange),
                  SizedBox(width: 8),
                  Text('No AI features available', style: TextStyle(fontSize: 12)),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildTestActionsCard(AIRideProvider aiProvider) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'AI Test Actions',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _testDynamicPricing(aiProvider),
                    icon: const Icon(Icons.attach_money),
                    label: const Text('Test Pricing'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _testRideMatching(aiProvider),
                    icon: const Icon(Icons.people),
                    label: const Text('Test Matching'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () => _testSmartRideRequest(aiProvider),
                icon: const Icon(Icons.auto_awesome),
                label: const Text('Test Smart Ride Request'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.purple,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPricingTestCard() {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'AI Dynamic Pricing Demo',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            AIDynamicPricingWidget(
              rideRequest: _testRideRequest,
              onPriceCalculated: (pricing) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      'AI Pricing: ${pricing['final_price']} SAR',
                    ),
                    backgroundColor: Colors.green,
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildResultsCard(AIRideProvider aiProvider) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Test Results',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            if (aiProvider.dynamicPricing != null) ...[
              _buildResultItem(
                'Dynamic Pricing',
                '${aiProvider.dynamicPricing!['final_price']} SAR',
                Icons.attach_money,
                Colors.green,
              ),
            ],
            if (aiProvider.aiMatches.isNotEmpty) ...[
              _buildResultItem(
                'Driver Matches',
                '${aiProvider.aiMatches.length} matches found',
                Icons.people,
                Colors.blue,
              ),
            ],
            if (aiProvider.error != null) ...[
              _buildResultItem(
                'Last Error',
                aiProvider.error!,
                Icons.error,
                Colors.red,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStatusRow(String label, String value, bool isPositive) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text('$label:', style: const TextStyle(fontWeight: FontWeight.w500)),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
          decoration: BoxDecoration(
            color: (isPositive ? Colors.green : Colors.red).withOpacity(0.1),
            borderRadius: BorderRadius.circular(4),
          ),
          child: Text(
            value,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: isPositive ? Colors.green : Colors.red,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildResultItem(String title, String value, IconData icon, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(icon, size: 16, color: color),
          const SizedBox(width: 8),
          Text('$title:', style: const TextStyle(fontWeight: FontWeight.w500)),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              value,
              style: TextStyle(color: color, fontWeight: FontWeight.bold),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _testDynamicPricing(AIRideProvider aiProvider) async {
    try {
      await aiProvider.calculateAIDynamicPrice(
        rideRequest: _testRideRequest,
        marketData: {
          'demand_level': 'high',
          'weather_condition': 'clear',
          'is_peak_hour': true,
        },
      );
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('✅ Dynamic pricing test completed!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Pricing test failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _testRideMatching(AIRideProvider aiProvider) async {
    try {
      // Create mock drivers for testing
      final mockDrivers = [
        // Mock driver data would go here
      ];
      
      await aiProvider.findAIMatches(
        rideRequest: _testRideRequest,
        availableDrivers: mockDrivers,
      );
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('✅ Ride matching test completed!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Matching test failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _testSmartRideRequest(AIRideProvider aiProvider) async {
    try {
      await aiProvider.processSmartRideRequest(
        rideRequest: _testRideRequest,
        availableDrivers: [],
        marketData: {
          'demand_level': 'medium',
          'weather_condition': 'clear',
        },
      );
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('✅ Smart ride request test completed!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Smart request test failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
