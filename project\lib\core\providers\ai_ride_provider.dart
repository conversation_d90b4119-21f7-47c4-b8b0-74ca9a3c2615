import 'package:flutter/foundation.dart';
import '../models/ride_request.dart';
import '../models/driver_model.dart';
import '../services/ai_service.dart';

/// Provider for AI-powered ride matching and management
class AIRideProvider with ChangeNotifier {
  bool _isLoading = false;
  bool _aiServiceConnected = false;
  List<Map<String, dynamic>> _aiMatches = [];
  Map<String, dynamic>? _dynamicPricing;
  Map<String, dynamic>? _safetyMonitoring;
  String? _error;

  // Getters
  bool get isLoading => _isLoading;
  bool get aiServiceConnected => _aiServiceConnected;
  List<Map<String, dynamic>> get aiMatches => _aiMatches;
  Map<String, dynamic>? get dynamicPricing => _dynamicPricing;
  Map<String, dynamic>? get safetyMonitoring => _safetyMonitoring;
  String? get error => _error;

  /// Initialize AI service connection
  Future<void> initializeAIService() async {
    _setLoading(true);
    try {
      _aiServiceConnected = await AIService.testConnection();
      if (_aiServiceConnected) {
        print('✅ AI Service connected successfully');
      } else {
        print('❌ AI Service connection failed');
      }
    } catch (e) {
      _setError('Failed to connect to AI service: $e');
      _aiServiceConnected = false;
    }
    _setLoading(false);
  }

  /// Find AI-powered optimal matches for a ride request
  Future<List<Map<String, dynamic>>> findAIMatches({
    required RideRequest rideRequest,
    required List<DriverModel> availableDrivers,
  }) async {
    if (!_aiServiceConnected) {
      print('⚠️ AI Service not connected, using fallback matching');
      return _fallbackMatching(rideRequest, availableDrivers);
    }

    _setLoading(true);
    _clearError();

    try {
      final matches = await AIService.findOptimalMatches(
        rideRequest: rideRequest,
        availableDrivers: availableDrivers,
      );

      _aiMatches = matches;
      print('🤖 AI found ${matches.length} optimal matches');
      
      // Add AI insights to each match
      for (var match in _aiMatches) {
        match['ai_powered'] = true;
        match['match_confidence'] = _calculateMatchConfidence(match);
        match['ai_recommendation'] = _generateAIRecommendation(match);
      }

      notifyListeners();
      return _aiMatches;
    } catch (e) {
      _setError('AI matching failed: $e');
      print('❌ AI matching error, using fallback: $e');
      return _fallbackMatching(rideRequest, availableDrivers);
    } finally {
      _setLoading(false);
    }
  }

  /// Calculate dynamic pricing using AI
  Future<Map<String, dynamic>?> calculateAIDynamicPrice({
    required RideRequest rideRequest,
    Map<String, dynamic>? marketData,
  }) async {
    if (!_aiServiceConnected) {
      return _fallbackPricing(rideRequest);
    }

    _setLoading(true);
    _clearError();

    try {
      final pricing = await AIService.calculateDynamicPrice(
        rideRequest: rideRequest,
        marketData: marketData,
      );

      if (pricing != null) {
        _dynamicPricing = pricing;
        _dynamicPricing!['ai_powered'] = true;
        _dynamicPricing!['price_explanation'] = _generatePriceExplanation(pricing);
        
        print('💰 AI calculated dynamic price: ${pricing['final_price']} SAR');
        notifyListeners();
      }

      return _dynamicPricing;
    } catch (e) {
      _setError('AI pricing failed: $e');
      print('❌ AI pricing error, using fallback: $e');
      return _fallbackPricing(rideRequest);
    } finally {
      _setLoading(false);
    }
  }

  /// Process smart ride request with multiple AI systems
  Future<Map<String, dynamic>?> processSmartRideRequest({
    required RideRequest rideRequest,
    required List<DriverModel> availableDrivers,
    Map<String, dynamic>? marketData,
  }) async {
    if (!_aiServiceConnected) {
      print('⚠️ AI Service not connected, processing without AI');
      return null;
    }

    _setLoading(true);
    _clearError();

    try {
      final result = await AIService.processSmartRideRequest(
        rideRequest: rideRequest,
        availableDrivers: availableDrivers,
        marketData: marketData,
      );

      if (result != null) {
        // Store AI results
        _dynamicPricing = result['pricing'];
        _aiMatches = List<Map<String, dynamic>>.from(result['matches'] ?? []);
        _safetyMonitoring = result['safety_preparation'];

        print('🚀 Smart ride request processed with AI');
        notifyListeners();
      }

      return result;
    } catch (e) {
      _setError('Smart ride request failed: $e');
      print('❌ Smart ride request error: $e');
      return null;
    } finally {
      _setLoading(false);
    }
  }

  /// Start AI-powered safety monitoring
  Future<bool> startAISafetyMonitoring({
    required String rideId,
    required String driverId,
    required String riderId,
    required Map<String, double> pickupLocation,
    required Map<String, double> dropoffLocation,
    required int estimatedDuration,
  }) async {
    if (!_aiServiceConnected) {
      print('⚠️ AI Service not connected, safety monitoring disabled');
      return false;
    }

    try {
      final result = await AIService.startSafetyMonitoring(
        rideId: rideId,
        driverId: driverId,
        riderId: riderId,
        pickupLocation: pickupLocation,
        dropoffLocation: dropoffLocation,
        estimatedDuration: estimatedDuration,
      );

      if (result != null && result['monitoring_started'] == true) {
        _safetyMonitoring = result;
        print('🛡️ AI safety monitoring started for ride $rideId');
        notifyListeners();
        return true;
      }

      return false;
    } catch (e) {
      _setError('Safety monitoring failed: $e');
      print('❌ Safety monitoring error: $e');
      return false;
    }
  }

  /// Update ride location for AI safety monitoring
  Future<void> updateRideLocationForAI({
    required String rideId,
    required double latitude,
    required double longitude,
    required double speed,
  }) async {
    if (!_aiServiceConnected || _safetyMonitoring == null) return;

    try {
      final result = await AIService.updateRideLocation(
        rideId: rideId,
        latitude: latitude,
        longitude: longitude,
        speed: speed,
      );

      if (result != null) {
        // Check for safety alerts
        if (result['safety_alerts'] != null && result['safety_alerts'].isNotEmpty) {
          print('⚠️ Safety alert: ${result['safety_alerts']}');
          // Handle safety alerts (show notifications, etc.)
        }
      }
    } catch (e) {
      print('❌ Location update error: $e');
    }
  }

  /// Fallback matching when AI is not available
  List<Map<String, dynamic>> _fallbackMatching(
    RideRequest rideRequest,
    List<DriverModel> availableDrivers,
  ) {
    // Simple distance-based matching as fallback
    final matches = <Map<String, dynamic>>[];
    
    for (final driver in availableDrivers.take(5)) {
      matches.add({
        'driver_id': driver.id,
        'driver': driver.toJson(),
        'match_score': 0.8, // Default score
        'pickup_distance': 2.5, // Default distance
        'pickup_eta': 5, // Default ETA
        'ai_powered': false,
        'fallback_match': true,
      });
    }

    return matches;
  }

  /// Fallback pricing when AI is not available
  Map<String, dynamic> _fallbackPricing(RideRequest rideRequest) {
    final distance = rideRequest.estimatedDistance ?? 5.0;
    final basePrice = distance * 2.0; // 2 SAR per km
    
    return {
      'base_price': basePrice,
      'final_price': basePrice * 1.1, // 10% markup
      'surge_multiplier': 1.1,
      'ai_powered': false,
      'fallback_pricing': true,
    };
  }

  /// Calculate match confidence score
  double _calculateMatchConfidence(Map<String, dynamic> match) {
    final matchScore = match['match_score'] ?? 0.0;
    final pickupDistance = match['pickup_distance'] ?? 10.0;
    
    // Higher score and lower distance = higher confidence
    final confidence = (matchScore * 0.7) + ((10 - pickupDistance) / 10 * 0.3);
    return (confidence * 100).clamp(0, 100);
  }

  /// Generate AI recommendation text
  String _generateAIRecommendation(Map<String, dynamic> match) {
    final confidence = _calculateMatchConfidence(match);
    final matchScore = match['match_score'] ?? 0.0;
    
    if (confidence > 90) {
      return 'Excellent match! High compatibility and close proximity.';
    } else if (confidence > 75) {
      return 'Good match with optimal route and timing.';
    } else if (confidence > 60) {
      return 'Suitable match with reasonable distance.';
    } else {
      return 'Available option with longer pickup time.';
    }
  }

  /// Generate price explanation
  String _generatePriceExplanation(Map<String, dynamic> pricing) {
    final surgeMultiplier = pricing['surge_multiplier'] ?? 1.0;
    
    if (surgeMultiplier > 1.5) {
      return 'High demand pricing due to peak hours and weather conditions.';
    } else if (surgeMultiplier > 1.2) {
      return 'Moderate demand pricing with slight increase.';
    } else {
      return 'Standard pricing with current market conditions.';
    }
  }

  /// Clear AI data
  void clearAIData() {
    _aiMatches.clear();
    _dynamicPricing = null;
    _safetyMonitoring = null;
    _clearError();
    notifyListeners();
  }

  /// Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
  }

  /// Get AI service status info
  Map<String, dynamic> getAIServiceInfo() {
    return {
      'connected': _aiServiceConnected,
      'features_available': _aiServiceConnected ? [
        'Smart Ride Matching',
        'Dynamic Pricing',
        'Safety Monitoring',
        'Predictive Maintenance',
      ] : [],
      'last_error': _error,
    };
  }
}
