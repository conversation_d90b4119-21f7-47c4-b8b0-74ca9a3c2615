{"checksum": "917ab985358331bbb72b69ebcc5fdc02", "roots": {"bookmark_bar": {"children": [{"date_added": "13373753705520724", "date_last_used": "0", "guid": "bd993b52-d375-4b78-a7cb-88ca72ce91aa", "id": "6", "name": "Real-Time Location Tracking System with Flutter, Node.js and Socket.IO | by <PERSON><PERSON><PERSON> | Medium", "type": "url", "url": "https://medium.com/@berkekurnaz/real-time-location-tracking-system-with-flutter-node-js-and-socket-io-0f00579ce629"}, {"date_added": "13383073239427624", "date_last_used": "0", "guid": "50f287f7-c26e-478a-92bd-d779420f781b", "id": "7", "name": "https://www.figma.com/design/z1eDlc4IJh1mbh8iYWG9Ia/UBER-APP-DESIGN-%26-PROTOTYPE-(Community)?node-id=33-6&t=6LUJMrr3VXhDvH9t-0", "type": "url", "url": "https://www.figma.com/design/z1eDlc4IJh1mbh8iYWG9Ia/UBER-APP-DESIGN-%26-PROTOTYPE-(Community)?node-id=33-6&t=6LUJMrr3VXhDvH9t-0"}, {"date_added": "13383073256096872", "date_last_used": "0", "guid": "74022ca2-2661-4690-ab51-3f1c2dfd6210", "id": "8", "name": "https://www.dailymotion.com/video/x8ruv99", "type": "url", "url": "https://www.dailymotion.com/video/x8ruv99"}, {"date_added": "13383747742723182", "date_last_used": "13393005128355886", "guid": "56c781a5-a98f-471e-afa7-4527cd201ce1", "id": "9", "name": "https://open.spotify.com/", "type": "url", "url": "https://open.spotify.com/"}, {"date_added": "13384028299448785", "date_last_used": "0", "guid": "c88dbb85-fa60-4145-a3e7-f6633911a3b1", "id": "10", "name": "https://docs.github.com/en/get-started/writing-on-github/getting-started-with-writing-and-formatting-on-github/basic-writing-and-formatting-syntax", "type": "url", "url": "https://docs.github.com/en/get-started/writing-on-github/getting-started-with-writing-and-formatting-on-github/basic-writing-and-formatting-syntax"}, {"date_added": "13384181386358317", "date_last_used": "0", "guid": "bd536374-535f-45a8-809c-e7e6ac11abd0", "id": "11", "name": "https://www.pradipdebnath.com/2020/10/04/how-to-implement-email-authentication-in-react-native-using-firebase/", "type": "url", "url": "https://www.pradipdebnath.com/2020/10/04/how-to-implement-email-authentication-in-react-native-using-firebase/"}, {"date_added": "13384605623838882", "date_last_used": "0", "guid": "08d2dc9f-8927-4ce1-85e8-d4dc442cdf61", "id": "12", "name": "https://pub.dev/", "type": "url", "url": "https://pub.dev/"}, {"date_added": "13385049413183467", "date_last_used": "0", "guid": "eaf89f92-a658-4322-aaaf-16443884bc34", "id": "13", "name": "https://play.google.com/console/u/1/developers/7931969641797171837/app/4972881873366431155/policy-center/issues/4987136894415054053/details", "type": "url", "url": "https://play.google.com/console/u/1/developers/7931969641797171837/app/4972881873366431155/policy-center/issues/4987136894415054053/details"}, {"date_added": "13385498158558517", "date_last_used": "13385553279990045", "guid": "a87c4578-236c-4f34-a179-31d425a74188", "id": "14", "name": "https://github.com/signup?return_to=https%3A%2F%2Fgithub.com%2FDhiWise&source=login", "type": "url", "url": "https://github.com/signup?return_to=https%3A%2F%2Fgithub.com%2FDhiWise&source=login"}, {"date_added": "13385642104522910", "date_last_used": "0", "guid": "1038085b-e222-4b82-9fcd-44798dab5063", "id": "15", "name": "https://bitbucket.org/gogrow-development/ds-stay-app/src/master/", "type": "url", "url": "https://bitbucket.org/gogrow-development/ds-stay-app/src/master/"}, {"date_added": "13386725588346268", "date_last_used": "0", "guid": "314d50c9-4f3d-46c4-abd2-643051b46264", "id": "16", "meta_info": {"power_bookmark_meta": ""}, "name": "(94) WhatsApp", "type": "url", "url": "https://web.whatsapp.com/"}, {"date_added": "13389744594112988", "date_last_used": "0", "guid": "da8159db-8da7-4216-b148-1c9f62b47b22", "id": "17", "name": "https://www.finalspaceapi.com/docs/", "type": "url", "url": "https://www.finalspaceapi.com/docs/"}, {"date_added": "13391294770398697", "date_last_used": "0", "guid": "28a0d684-898c-4995-b969-1ab78c93e5aa", "id": "18", "name": "https://app.wiremock.cloud/mock-apis/create-flow", "type": "url", "url": "https://app.wiremock.cloud/mock-apis/create-flow"}], "date_added": "13393019174755782", "date_last_used": "0", "date_modified": "13391294770398697", "guid": "0bc5d13f-2cba-5d74-951f-3f233fe6c908", "id": "1", "name": "Bookmarks bar", "type": "folder"}, "other": {"children": [], "date_added": "13393019174755785", "date_last_used": "0", "date_modified": "0", "guid": "82b081ec-3dd3-529c-8475-ab6c344590dd", "id": "2", "name": "Other bookmarks", "type": "folder"}, "synced": {"children": [], "date_added": "13393019174755787", "date_last_used": "0", "date_modified": "0", "guid": "4cf2e351-0e85-532b-bb37-df045d8f8d0f", "id": "3", "name": "Mobile bookmarks", "type": "folder"}}, "version": 1}