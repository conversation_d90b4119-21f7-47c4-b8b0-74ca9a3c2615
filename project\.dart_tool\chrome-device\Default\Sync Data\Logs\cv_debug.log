{"logTime": "0529/190617", "correlationVector":"wOtsoHAXD/t0cWOPYuOmqd.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_francecentral_prod-s01-062-eur-francecentral", "migrationStage":"NotStarted", "server":"akswtt00800002w"}}
{"logTime": "0529/190617", "correlationVector":"wOtsoHAXD/t0cWOPYuOmqd.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Encryption Keys", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"1", "total":"1"}}
{"logTime": "0529/190617", "correlationVector":"+iXWKKv2IbZHSOTm6YCQN9","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"", "migrationStage":"", "server":""}}
{"logTime": "0529/190617", "correlationVector":"+iXWKKv2IbZHSOTm6YCQN9.0","action":"EncryptionKeysFetcher.DownloadKeys:", "result":"Success", "context":Key count: 2, Last key timestamp: 2025-04-16T09:13:48Z}
{"logTime": "0529/190617", "correlationVector":"+iXWKKv2IbZHSOTm6YCQN9.1","action":"EncryptionKeysManager::SetEncryptionKeys:", "result":"Success", "context":Key names[2]:[DkdqF7Jopw0I4ET1mSnmxkGHxoI8+CvoHpEpTxoNsgTLcAWHsgyJ4llf9zgCn0pgmjdIF+EwvCfB82qmzvQQTw==][jOE6+7W5dVBk0livdNbuucMSJZhttSumB1lMCxc4sPjoKujdNzL/6Xr6ecvpZZPEHXDi2fcbENNNnybstDtpog==]}
{"logTime": "0529/190617", "correlationVector":"+iXWKKv2IbZHSOTm6YCQN9.2","action":"EncryptionKeysManager::SetEncryptionKeysWithTimestamps:", "result":"Success", "context":Key timestamps[2]:[2024-10-17T20:17:54Z][2025-04-16T09:13:48Z]}
{"logTime": "0529/190617", "correlationVector":"wOtsoHAXD/t0cWOPYuOmqd","action":"Initial GetUpdates", "result":"", "context":Reason: NEW_CLIENT. cV=wOtsoHAXD/t0cWOPYuOmqd}
{"logTime": "0529/190617", "correlationVector":"wOtsoHAXD/t0cWOPYuOmqd.3","action":"GetUpdates Response", "result":"Success", "context":Received 1 update(s). cV=wOtsoHAXD/t0cWOPYuOmqd.0;server=akswtt00800002w;cloudType=Consumer;environment=Prod_francecentral_prod-s01-062-eur-francecentral;migrationStage=NotStarted}
{"logTime": "0529/190617", "correlationVector":"ecWfA0HiydiQkJn/CBJSA4","action":"Initial GetUpdates", "result":"", "context":Reason: NEWLY_SUPPORTED_DATATYPE. cV=ecWfA0HiydiQkJn/CBJSA4}
{"logTime": "0529/190618", "correlationVector":"ecWfA0HiydiQkJn/CBJSA4.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_francecentral_prod-s01-062-eur-francecentral", "migrationStage":"NotStarted", "server":"akswtt008000015"}}
{"logTime": "0529/190618", "correlationVector":"ecWfA0HiydiQkJn/CBJSA4.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Preferences", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"74", "total":"74"}}
{"logTime": "0529/190618", "correlationVector":"ecWfA0HiydiQkJn/CBJSA4.3","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Passwords", "deleted":"1", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"19", "total":"19"}}
{"logTime": "0529/190618", "correlationVector":"ecWfA0HiydiQkJn/CBJSA4.4","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Device Info", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"11", "total":"11"}}
{"logTime": "0529/190618", "correlationVector":"ecWfA0HiydiQkJn/CBJSA4.5","action":"GetUpdates Response", "result":"Success", "context":Received 104 update(s). cV=ecWfA0HiydiQkJn/CBJSA4.0;server=akswtt008000015;cloudType=Consumer;environment=Prod_francecentral_prod-s01-062-eur-francecentral;migrationStage=NotStarted}
{"logTime": "0529/190618", "correlationVector":"WVWY9EsVPKXzQQKP4plHnX","action":"Initial GetUpdates", "result":"", "context":Reason: NEWLY_SUPPORTED_DATATYPE. cV=WVWY9EsVPKXzQQKP4plHnX}
{"logTime": "0529/190618", "correlationVector":"WVWY9EsVPKXzQQKP4plHnX.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_francecentral_prod-s01-062-eur-francecentral", "migrationStage":"NotStarted", "server":"akswtt008000037"}}
{"logTime": "0529/190618", "correlationVector":"WVWY9EsVPKXzQQKP4plHnX.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Bookmarks", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"18", "total":"18"}}
{"logTime": "0529/190618", "correlationVector":"WVWY9EsVPKXzQQKP4plHnX.3","action":"GetUpdates Response", "result":"Success", "context":Received 18 update(s). cV=WVWY9EsVPKXzQQKP4plHnX.0;server=akswtt008000037;cloudType=Consumer;environment=Prod_francecentral_prod-s01-062-eur-francecentral;migrationStage=NotStarted}
{"logTime": "0529/190618", "correlationVector":"JZHjJVxmjJH51F7Ppf4b+1","action":"Initial GetUpdates", "result":"", "context":Reason: NEWLY_SUPPORTED_DATATYPE. cV=JZHjJVxmjJH51F7Ppf4b+1}
{"logTime": "0529/190619", "correlationVector":"JZHjJVxmjJH51F7Ppf4b+1.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_francecentral_prod-s01-062-eur-francecentral", "migrationStage":"NotStarted", "server":"akswtt00800001u"}}
{"logTime": "0529/190619", "correlationVector":"JZHjJVxmjJH51F7Ppf4b+1.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Autofill", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"1", "total":"1"}}
{"logTime": "0529/190619", "correlationVector":"JZHjJVxmjJH51F7Ppf4b+1.3","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Sessions", "deleted":"248", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"248", "total":"248"}}
{"logTime": "0529/190619", "correlationVector":"JZHjJVxmjJH51F7Ppf4b+1.4","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Edge Hub App Usage", "deleted":"1", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"1", "total":"1"}}
{"logTime": "0529/190619", "correlationVector":"JZHjJVxmjJH51F7Ppf4b+1.5","action":"GetUpdates Response", "result":"Success", "context":Received 250 update(s). cV=JZHjJVxmjJH51F7Ppf4b+1.0;server=akswtt00800001u;cloudType=Consumer;environment=Prod_francecentral_prod-s01-062-eur-francecentral;migrationStage=NotStarted Some updates remain.}
{"logTime": "0529/190619", "correlationVector":"g/ykZUVdjIz2jLNs1ZEy67","action":"Initial GetUpdates", "result":"", "context":Reason: NEWLY_SUPPORTED_DATATYPE. cV=g/ykZUVdjIz2jLNs1ZEy67}
{"logTime": "0529/190620", "correlationVector":"g/ykZUVdjIz2jLNs1ZEy67.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_francecentral_prod-s01-062-eur-francecentral", "migrationStage":"NotStarted", "server":"akswtt008000036"}}
{"logTime": "0529/190620", "correlationVector":"g/ykZUVdjIz2jLNs1ZEy67.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Extensions", "deleted":"2", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"2", "total":"2"}}
{"logTime": "0529/190620", "correlationVector":"g/ykZUVdjIz2jLNs1ZEy67.3","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Sessions", "deleted":"246", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"246", "total":"246"}}
{"logTime": "0529/190620", "correlationVector":"g/ykZUVdjIz2jLNs1ZEy67.4","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Extension settings", "deleted":"1", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"1", "total":"1"}}
{"logTime": "0529/190620", "correlationVector":"g/ykZUVdjIz2jLNs1ZEy67.5","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Edge Tab Group", "deleted":"1", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"1", "total":"1"}}
{"logTime": "0529/190620", "correlationVector":"g/ykZUVdjIz2jLNs1ZEy67.6","action":"GetUpdates Response", "result":"Success", "context":Received 250 update(s). cV=g/ykZUVdjIz2jLNs1ZEy67.0;server=akswtt008000036;cloudType=Consumer;environment=Prod_francecentral_prod-s01-062-eur-francecentral;migrationStage=NotStarted Some updates remain.}
{"logTime": "0529/190620", "correlationVector":"ELFwyvPx0Rc9pNxN9RCmJT","action":"Initial GetUpdates", "result":"", "context":Reason: NEWLY_SUPPORTED_DATATYPE. cV=ELFwyvPx0Rc9pNxN9RCmJT}
{"logTime": "0529/190620", "correlationVector":"ELFwyvPx0Rc9pNxN9RCmJT.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_francecentral_prod-s01-062-eur-francecentral", "migrationStage":"NotStarted", "server":"akswtt008000008"}}
{"logTime": "0529/190620", "correlationVector":"ELFwyvPx0Rc9pNxN9RCmJT.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Autofill Profiles", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"5", "total":"5"}}
{"logTime": "0529/190620", "correlationVector":"ELFwyvPx0Rc9pNxN9RCmJT.3","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Autofill", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"29", "total":"29"}}
{"logTime": "0529/190620", "correlationVector":"ELFwyvPx0Rc9pNxN9RCmJT.4","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Extensions", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"3", "total":"3"}}
{"logTime": "0529/190620", "correlationVector":"ELFwyvPx0Rc9pNxN9RCmJT.5","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Sessions", "deleted":"211", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"211", "total":"211"}}
{"logTime": "0529/190620", "correlationVector":"ELFwyvPx0Rc9pNxN9RCmJT.6","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Edge Hub App Usage", "deleted":"1", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"2", "total":"2"}}
{"logTime": "0529/190620", "correlationVector":"ELFwyvPx0Rc9pNxN9RCmJT.7","action":"GetUpdates Response", "result":"Success", "context":Received 250 update(s). cV=ELFwyvPx0Rc9pNxN9RCmJT.0;server=akswtt008000008;cloudType=Consumer;environment=Prod_francecentral_prod-s01-062-eur-francecentral;migrationStage=NotStarted Some updates remain.}
{"logTime": "0529/190620", "correlationVector":"kmM031J5gh0wrdlYTclNXD","action":"Initial GetUpdates", "result":"", "context":Reason: NEWLY_SUPPORTED_DATATYPE. cV=kmM031J5gh0wrdlYTclNXD}
{"logTime": "0529/190621", "correlationVector":"kmM031J5gh0wrdlYTclNXD.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_francecentral_prod-s01-062-eur-francecentral", "migrationStage":"NotStarted", "server":"akswtt008000036"}}
{"logTime": "0529/190621", "correlationVector":"kmM031J5gh0wrdlYTclNXD.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Autofill Profiles", "deleted":"1", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"5", "total":"5"}}
{"logTime": "0529/190621", "correlationVector":"kmM031J5gh0wrdlYTclNXD.3","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Autofill", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"112", "total":"112"}}
{"logTime": "0529/190621", "correlationVector":"kmM031J5gh0wrdlYTclNXD.4","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Sessions", "deleted":"133", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"133", "total":"133"}}
{"logTime": "0529/190621", "correlationVector":"kmM031J5gh0wrdlYTclNXD.5","action":"GetUpdates Response", "result":"Success", "context":Received 250 update(s). cV=kmM031J5gh0wrdlYTclNXD.0;server=akswtt008000036;cloudType=Consumer;environment=Prod_francecentral_prod-s01-062-eur-francecentral;migrationStage=NotStarted Some updates remain.}
{"logTime": "0529/190621", "correlationVector":"yrv5miLbpuCS+kcnfOBXCI","action":"Initial GetUpdates", "result":"", "context":Reason: NEWLY_SUPPORTED_DATATYPE. cV=yrv5miLbpuCS+kcnfOBXCI}
{"logTime": "0529/190621", "correlationVector":"yrv5miLbpuCS+kcnfOBXCI.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_francecentral_prod-s01-062-eur-francecentral", "migrationStage":"NotStarted", "server":"akswtt00800003g"}}
{"logTime": "0529/190621", "correlationVector":"yrv5miLbpuCS+kcnfOBXCI.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Autofill Profiles", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"1", "total":"1"}}
{"logTime": "0529/190621", "correlationVector":"yrv5miLbpuCS+kcnfOBXCI.3","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Autofill", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"6", "total":"6"}}
{"logTime": "0529/190621", "correlationVector":"yrv5miLbpuCS+kcnfOBXCI.4","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Extensions", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"1", "total":"1"}}
{"logTime": "0529/190621", "correlationVector":"yrv5miLbpuCS+kcnfOBXCI.5","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Sessions", "deleted":"227", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"227", "total":"227"}}
{"logTime": "0529/190621", "correlationVector":"yrv5miLbpuCS+kcnfOBXCI.6","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"History Delete Directives", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"3", "total":"3"}}
{"logTime": "0529/190621", "correlationVector":"yrv5miLbpuCS+kcnfOBXCI.7","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Saved Tab Group", "deleted":"4", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"7", "total":"7"}}
{"logTime": "0529/190621", "correlationVector":"yrv5miLbpuCS+kcnfOBXCI.8","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Edge Hub App Usage", "deleted":"1", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"4", "total":"4"}}
{"logTime": "0529/190621", "correlationVector":"yrv5miLbpuCS+kcnfOBXCI.9","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Edge Tab Group", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"1", "total":"1"}}
{"logTime": "0529/190621", "correlationVector":"yrv5miLbpuCS+kcnfOBXCI.10","action":"GetUpdates Response", "result":"Success", "context":Received 250 update(s). cV=yrv5miLbpuCS+kcnfOBXCI.0;server=akswtt00800003g;cloudType=Consumer;environment=Prod_francecentral_prod-s01-062-eur-francecentral;migrationStage=NotStarted Some updates remain.}
{"logTime": "0529/190621", "correlationVector":"WG8wanF8v+5p+nBIDwtrMC","action":"Initial GetUpdates", "result":"", "context":Reason: NEWLY_SUPPORTED_DATATYPE. cV=WG8wanF8v+5p+nBIDwtrMC}
{"logTime": "0529/190622", "correlationVector":"WG8wanF8v+5p+nBIDwtrMC.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_francecentral_prod-s01-062-eur-francecentral", "migrationStage":"NotStarted", "server":"akswtt008000049"}}
{"logTime": "0529/190622", "correlationVector":"WG8wanF8v+5p+nBIDwtrMC.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Autofill Profiles", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"2", "total":"2"}}
{"logTime": "0529/190622", "correlationVector":"WG8wanF8v+5p+nBIDwtrMC.3","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Autofill", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"5", "total":"5"}}
{"logTime": "0529/190622", "correlationVector":"WG8wanF8v+5p+nBIDwtrMC.4","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Sessions", "deleted":"218", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"218", "total":"218"}}
{"logTime": "0529/190622", "correlationVector":"WG8wanF8v+5p+nBIDwtrMC.5","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Saved Tab Group", "deleted":"9", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"22", "total":"22"}}
{"logTime": "0529/190622", "correlationVector":"WG8wanF8v+5p+nBIDwtrMC.6","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Edge Hub App Usage", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"1", "total":"1"}}
{"logTime": "0529/190622", "correlationVector":"WG8wanF8v+5p+nBIDwtrMC.7","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Edge Tab Group", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"2", "total":"2"}}
{"logTime": "0529/190622", "correlationVector":"WG8wanF8v+5p+nBIDwtrMC.8","action":"GetUpdates Response", "result":"Success", "context":Received 250 update(s). cV=WG8wanF8v+5p+nBIDwtrMC.0;server=akswtt008000049;cloudType=Consumer;environment=Prod_francecentral_prod-s01-062-eur-francecentral;migrationStage=NotStarted Some updates remain.}
{"logTime": "0529/190622", "correlationVector":"2GUURnBOUq5/jSByrnnPy9","action":"Initial GetUpdates", "result":"", "context":Reason: NEWLY_SUPPORTED_DATATYPE. cV=2GUURnBOUq5/jSByrnnPy9}
{"logTime": "0529/190622", "correlationVector":"2GUURnBOUq5/jSByrnnPy9.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_francecentral_prod-s01-062-eur-francecentral", "migrationStage":"NotStarted", "server":"akswtt00800001u"}}
{"logTime": "0529/190622", "correlationVector":"2GUURnBOUq5/jSByrnnPy9.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Autofill Profiles", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"3", "total":"3"}}
{"logTime": "0529/190622", "correlationVector":"2GUURnBOUq5/jSByrnnPy9.3","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Autofill", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"8", "total":"8"}}
{"logTime": "0529/190622", "correlationVector":"2GUURnBOUq5/jSByrnnPy9.4","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Extensions", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"1", "total":"1"}}
{"logTime": "0529/190622", "correlationVector":"2GUURnBOUq5/jSByrnnPy9.5","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Sessions", "deleted":"138", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"144", "total":"144"}}
{"logTime": "0529/190622", "correlationVector":"2GUURnBOUq5/jSByrnnPy9.6","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Saved Tab Group", "deleted":"9", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"27", "total":"27"}}
{"logTime": "0529/190622", "correlationVector":"2GUURnBOUq5/jSByrnnPy9.7","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Edge Tab Group", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"2", "total":"2"}}
{"logTime": "0529/190622", "correlationVector":"2GUURnBOUq5/jSByrnnPy9.8","action":"GetUpdates Response", "result":"Success", "context":Received 185 update(s). cV=2GUURnBOUq5/jSByrnnPy9.0;server=akswtt00800001u;cloudType=Consumer;environment=Prod_francecentral_prod-s01-062-eur-francecentral;migrationStage=NotStarted}
{"logTime": "0529/190622", "correlationVector":"36ZGkVrFVUifiPwTFzJNtO","action":"Initial GetUpdates", "result":"", "context":Reason: NEWLY_SUPPORTED_DATATYPE. cV=36ZGkVrFVUifiPwTFzJNtO}
{"logTime": "0529/190623", "correlationVector":"36ZGkVrFVUifiPwTFzJNtO.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_francecentral_prod-s01-062-eur-francecentral", "migrationStage":"NotStarted", "server":"akswtt00800003g"}}
{"logTime": "0529/190623", "correlationVector":"36ZGkVrFVUifiPwTFzJNtO.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"History", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"250", "total":"250"}}
{"logTime": "0529/190623", "correlationVector":"36ZGkVrFVUifiPwTFzJNtO.3","action":"GetUpdates Response", "result":"Success", "context":Received 250 update(s). cV=36ZGkVrFVUifiPwTFzJNtO.0;server=akswtt00800003g;cloudType=Consumer;environment=Prod_francecentral_prod-s01-062-eur-francecentral;migrationStage=NotStarted Some updates remain.}
{"logTime": "0529/190623", "correlationVector":"YQ6rGNLKpvhYKrLGw3qVmS","action":"Initial GetUpdates", "result":"", "context":Reason: NEWLY_SUPPORTED_DATATYPE. cV=YQ6rGNLKpvhYKrLGw3qVmS}
{"logTime": "0529/190623", "correlationVector":"YQ6rGNLKpvhYKrLGw3qVmS.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_francecentral_prod-s01-062-eur-francecentral", "migrationStage":"NotStarted", "server":"akswtt008000008"}}
{"logTime": "0529/190623", "correlationVector":"YQ6rGNLKpvhYKrLGw3qVmS.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"History", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"250", "total":"250"}}
{"logTime": "0529/190623", "correlationVector":"YQ6rGNLKpvhYKrLGw3qVmS.3","action":"GetUpdates Response", "result":"Success", "context":Received 250 update(s). cV=YQ6rGNLKpvhYKrLGw3qVmS.0;server=akswtt008000008;cloudType=Consumer;environment=Prod_francecentral_prod-s01-062-eur-francecentral;migrationStage=NotStarted Some updates remain.}
{"logTime": "0529/190623", "correlationVector":"HHv6EaxKneBy7SuiAelemj","action":"Initial GetUpdates", "result":"", "context":Reason: NEWLY_SUPPORTED_DATATYPE. cV=HHv6EaxKneBy7SuiAelemj}
{"logTime": "0529/190624", "correlationVector":"HHv6EaxKneBy7SuiAelemj.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_francecentral_prod-s01-062-eur-francecentral", "migrationStage":"NotStarted", "server":"akswtt008000049"}}
{"logTime": "0529/190624", "correlationVector":"HHv6EaxKneBy7SuiAelemj.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"History", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"250", "total":"250"}}
{"logTime": "0529/190624", "correlationVector":"HHv6EaxKneBy7SuiAelemj.3","action":"GetUpdates Response", "result":"Success", "context":Received 250 update(s). cV=HHv6EaxKneBy7SuiAelemj.0;server=akswtt008000049;cloudType=Consumer;environment=Prod_francecentral_prod-s01-062-eur-francecentral;migrationStage=NotStarted Some updates remain.}
{"logTime": "0529/190624", "correlationVector":"OsnKgahPovqPJw/hvYlYsC","action":"Initial GetUpdates", "result":"", "context":Reason: NEWLY_SUPPORTED_DATATYPE. cV=OsnKgahPovqPJw/hvYlYsC}
{"logTime": "0529/190625", "correlationVector":"OsnKgahPovqPJw/hvYlYsC.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_francecentral_prod-s01-062-eur-francecentral", "migrationStage":"NotStarted", "server":"akswtt008000052"}}
{"logTime": "0529/190625", "correlationVector":"OsnKgahPovqPJw/hvYlYsC.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"History", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"250", "total":"250"}}
{"logTime": "0529/190625", "correlationVector":"OsnKgahPovqPJw/hvYlYsC.3","action":"GetUpdates Response", "result":"Success", "context":Received 250 update(s). cV=OsnKgahPovqPJw/hvYlYsC.0;server=akswtt008000052;cloudType=Consumer;environment=Prod_francecentral_prod-s01-062-eur-francecentral;migrationStage=NotStarted Some updates remain.}
{"logTime": "0529/190625", "correlationVector":"tyHwgGtyUQPWZiJOycBLsU","action":"Initial GetUpdates", "result":"", "context":Reason: NEWLY_SUPPORTED_DATATYPE. cV=tyHwgGtyUQPWZiJOycBLsU}
{"logTime": "0529/190625", "correlationVector":"tyHwgGtyUQPWZiJOycBLsU.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_francecentral_prod-s01-062-eur-francecentral", "migrationStage":"NotStarted", "server":"akswtt00800003g"}}
{"logTime": "0529/190625", "correlationVector":"tyHwgGtyUQPWZiJOycBLsU.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"History", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"250", "total":"250"}}
{"logTime": "0529/190625", "correlationVector":"tyHwgGtyUQPWZiJOycBLsU.3","action":"GetUpdates Response", "result":"Success", "context":Received 250 update(s). cV=tyHwgGtyUQPWZiJOycBLsU.0;server=akswtt00800003g;cloudType=Consumer;environment=Prod_francecentral_prod-s01-062-eur-francecentral;migrationStage=NotStarted Some updates remain.}
{"logTime": "0529/190625", "correlationVector":"8Sa9MXpw0IxZWO0vOQnKJp","action":"Initial GetUpdates", "result":"", "context":Reason: NEWLY_SUPPORTED_DATATYPE. cV=8Sa9MXpw0IxZWO0vOQnKJp}
{"logTime": "0529/190626", "correlationVector":"8Sa9MXpw0IxZWO0vOQnKJp.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_francecentral_prod-s01-062-eur-francecentral", "migrationStage":"NotStarted", "server":"akswtt008000052"}}
{"logTime": "0529/190626", "correlationVector":"8Sa9MXpw0IxZWO0vOQnKJp.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"History", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"250", "total":"250"}}
{"logTime": "0529/190626", "correlationVector":"8Sa9MXpw0IxZWO0vOQnKJp.3","action":"GetUpdates Response", "result":"Success", "context":Received 250 update(s). cV=8Sa9MXpw0IxZWO0vOQnKJp.0;server=akswtt008000052;cloudType=Consumer;environment=Prod_francecentral_prod-s01-062-eur-francecentral;migrationStage=NotStarted Some updates remain.}
{"logTime": "0529/190626", "correlationVector":"riR1D6MMxVQzXcWLKS5si8","action":"Initial GetUpdates", "result":"", "context":Reason: NEWLY_SUPPORTED_DATATYPE. cV=riR1D6MMxVQzXcWLKS5si8}
{"logTime": "0529/190627", "correlationVector":"riR1D6MMxVQzXcWLKS5si8.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_francecentral_prod-s01-062-eur-francecentral", "migrationStage":"NotStarted", "server":"akswtt00800004b"}}
{"logTime": "0529/190627", "correlationVector":"riR1D6MMxVQzXcWLKS5si8.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"History", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"250", "total":"250"}}
{"logTime": "0529/190627", "correlationVector":"riR1D6MMxVQzXcWLKS5si8.3","action":"GetUpdates Response", "result":"Success", "context":Received 250 update(s). cV=riR1D6MMxVQzXcWLKS5si8.0;server=akswtt00800004b;cloudType=Consumer;environment=Prod_francecentral_prod-s01-062-eur-francecentral;migrationStage=NotStarted Some updates remain.}
{"logTime": "0529/190627", "correlationVector":"OY6NDfNQUx+/gEW1L0/aoS","action":"Initial GetUpdates", "result":"", "context":Reason: NEWLY_SUPPORTED_DATATYPE. cV=OY6NDfNQUx+/gEW1L0/aoS}
{"logTime": "0529/190627", "correlationVector":"OY6NDfNQUx+/gEW1L0/aoS.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_francecentral_prod-s01-062-eur-francecentral", "migrationStage":"NotStarted", "server":"akswtt008000008"}}
{"logTime": "0529/190627", "correlationVector":"OY6NDfNQUx+/gEW1L0/aoS.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"History", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"250", "total":"250"}}
{"logTime": "0529/190627", "correlationVector":"OY6NDfNQUx+/gEW1L0/aoS.3","action":"GetUpdates Response", "result":"Success", "context":Received 250 update(s). cV=OY6NDfNQUx+/gEW1L0/aoS.0;server=akswtt008000008;cloudType=Consumer;environment=Prod_francecentral_prod-s01-062-eur-francecentral;migrationStage=NotStarted Some updates remain.}
{"logTime": "0529/190627", "correlationVector":"94dN5nQ8OXO8j07C1pbRtT","action":"Initial GetUpdates", "result":"", "context":Reason: NEWLY_SUPPORTED_DATATYPE. cV=94dN5nQ8OXO8j07C1pbRtT}
{"logTime": "0529/190628", "correlationVector":"94dN5nQ8OXO8j07C1pbRtT.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_francecentral_prod-s01-062-eur-francecentral", "migrationStage":"NotStarted", "server":"akswtt008000036"}}
{"logTime": "0529/190628", "correlationVector":"94dN5nQ8OXO8j07C1pbRtT.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"History", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"250", "total":"250"}}
{"logTime": "0529/190628", "correlationVector":"94dN5nQ8OXO8j07C1pbRtT.3","action":"GetUpdates Response", "result":"Success", "context":Received 250 update(s). cV=94dN5nQ8OXO8j07C1pbRtT.0;server=akswtt008000036;cloudType=Consumer;environment=Prod_francecentral_prod-s01-062-eur-francecentral;migrationStage=NotStarted Some updates remain.}
{"logTime": "0529/190628", "correlationVector":"uTQAjXsLz9FXTGl5DV9iDe","action":"Initial GetUpdates", "result":"", "context":Reason: NEWLY_SUPPORTED_DATATYPE. cV=uTQAjXsLz9FXTGl5DV9iDe}
{"logTime": "0529/190628", "correlationVector":"uTQAjXsLz9FXTGl5DV9iDe.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_francecentral_prod-s01-062-eur-francecentral", "migrationStage":"NotStarted", "server":"akswtt00800001w"}}
{"logTime": "0529/190628", "correlationVector":"uTQAjXsLz9FXTGl5DV9iDe.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"History", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"250", "total":"250"}}
{"logTime": "0529/190628", "correlationVector":"uTQAjXsLz9FXTGl5DV9iDe.3","action":"GetUpdates Response", "result":"Success", "context":Received 250 update(s). cV=uTQAjXsLz9FXTGl5DV9iDe.0;server=akswtt00800001w;cloudType=Consumer;environment=Prod_francecentral_prod-s01-062-eur-francecentral;migrationStage=NotStarted Some updates remain.}
{"logTime": "0529/190628", "correlationVector":"B6AdXseuM/8Vxf5mWI6UOQ","action":"Initial GetUpdates", "result":"", "context":Reason: NEWLY_SUPPORTED_DATATYPE. cV=B6AdXseuM/8Vxf5mWI6UOQ}
{"logTime": "0529/190628", "correlationVector":"B6AdXseuM/8Vxf5mWI6UOQ.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_francecentral_prod-s01-062-eur-francecentral", "migrationStage":"NotStarted", "server":"akswtt00800004b"}}
{"logTime": "0529/190628", "correlationVector":"B6AdXseuM/8Vxf5mWI6UOQ.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"History", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"83", "total":"83"}}
{"logTime": "0529/190628", "correlationVector":"B6AdXseuM/8Vxf5mWI6UOQ.3","action":"GetUpdates Response", "result":"Success", "context":Received 83 update(s). cV=B6AdXseuM/8Vxf5mWI6UOQ.0;server=akswtt00800004b;cloudType=Consumer;environment=Prod_francecentral_prod-s01-062-eur-francecentral;migrationStage=NotStarted}
{"logTime": "0529/190629", "correlationVector":"bDMrhe1L+6ojAPc+VrQO+x","action":"Normal GetUpdate request", "result":"", "context":cV=bDMrhe1L+6ojAPc+VrQO+x
Nudged types: Preferences, Passwords, Sessions, Device Info, User Consents, History
Refresh requested types: Bookmarks, Preferences, Passwords, Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, Collection, Edge E Drop, Edge Hub App Usage, Edge Tab Group, Edge Wallet, Encryption Keys}
{"logTime": "0529/190629", "correlationVector":"bDMrhe1L+6ojAPc+VrQO+x.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_francecentral_prod-s01-062-eur-francecentral", "migrationStage":"NotStarted", "server":"akswtt00800003k"}}
{"logTime": "0529/190629", "correlationVector":"bDMrhe1L+6ojAPc+VrQO+x.2","action":"GetUpdates Response", "result":"Success", "context":Received 0 update(s). cV=bDMrhe1L+6ojAPc+VrQO+x.0;server=akswtt00800003k;cloudType=Consumer;environment=Prod_francecentral_prod-s01-062-eur-francecentral;migrationStage=NotStarted}
{"logTime": "0529/190629", "correlationVector":"dbRMwVM/AaGxCsxK3EJ/wb","action":"Commit Request", "result":"", "context":Item count: 19
Contributing types: Preferences, Passwords, Sessions, Device Info, User Consents, History}
{"logTime": "0529/190630", "correlationVector":"dbRMwVM/AaGxCsxK3EJ/wb.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_francecentral_prod-s01-062-eur-francecentral", "migrationStage":"NotStarted", "server":"akswtt00800003g"}}
{"logTime": "0529/190630", "correlationVector":"dbRMwVM/AaGxCsxK3EJ/wb.2","action":"Commit Response", "result":"Success", "context":Result: Success. cV=dbRMwVM/AaGxCsxK3EJ/wb.0;server=akswtt00800003g;cloudType=Consumer;environment=Prod_francecentral_prod-s01-062-eur-francecentral;migrationStage=NotStarted}
{"logTime": "0529/190646", "correlationVector":"ZUUp8HcvyPdL/pnuXvxwAK","action":"Commit Request", "result":"", "context":Item count: 1
Contributing types: Preferences}
{"logTime": "0529/190646", "correlationVector":"ZUUp8HcvyPdL/pnuXvxwAK.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_francecentral_prod-s01-062-eur-francecentral", "migrationStage":"NotStarted", "server":"akswtt008000008"}}
{"logTime": "0529/190646", "correlationVector":"ZUUp8HcvyPdL/pnuXvxwAK.2","action":"Commit Response", "result":"Success", "context":Result: Success. cV=ZUUp8HcvyPdL/pnuXvxwAK.0;server=akswtt008000008;cloudType=Consumer;environment=Prod_francecentral_prod-s01-062-eur-francecentral;migrationStage=NotStarted}
{"logTime": "0529/190756", "correlationVector":"Bmf0ufYd0OhVsosdTxqUeI","action":"Commit Request", "result":"", "context":Item count: 10
Contributing types: Sessions, History}
{"logTime": "0529/190757", "correlationVector":"Bmf0ufYd0OhVsosdTxqUeI.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_francecentral_prod-s01-062-eur-francecentral", "migrationStage":"NotStarted", "server":"akswtt008000049"}}
{"logTime": "0529/190757", "correlationVector":"Bmf0ufYd0OhVsosdTxqUeI.2","action":"Commit Response", "result":"Success", "context":Result: Success. cV=Bmf0ufYd0OhVsosdTxqUeI.0;server=akswtt008000049;cloudType=Consumer;environment=Prod_francecentral_prod-s01-062-eur-francecentral;migrationStage=NotStarted}
