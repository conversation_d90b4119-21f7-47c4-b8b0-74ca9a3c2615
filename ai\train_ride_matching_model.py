#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Advanced Ride Matching Model Trainer

This script generates comprehensive synthetic data and trains machine learning models
for the advanced ride matching system. It creates realistic training data and saves
optimized models to the models/ directory.

Models trained:
1. Traffic prediction model - Predicts travel time based on distance, time, weather, events
2. Compatibility model - Predicts rider-driver compatibility and satisfaction
3. Route optimizer model - Optimizes multi-passenger routes for efficiency
4. Demand prediction model - Predicts ride demand in different areas
5. Driver availability model - Predicts driver availability patterns

Features:
- Realistic synthetic data generation
- Advanced feature engineering
- Model optimization and validation
- Performance metrics and evaluation
- Model persistence and versioning

Author: Smart Ride-Sharing AI Team
"""

import numpy as np
import pandas as pd
from sklearn.ensemble import RandomForestRegressor, GradientBoostingClassifier
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, accuracy_score
import joblib
import os
import logging
from datetime import datetime, timedelta
import random
from typing import Dict, List, Tuple, Any
# import matplotlib.pyplot as plt  # Removed to avoid dependency issues

# Configure logging
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class RideMatchingModelTrainer:
    """Trains and saves models for ride matching system"""

    def __init__(self, models_dir: str = 'models'):
        """Initialize the model trainer"""
        self.models_dir = models_dir
        os.makedirs(models_dir, exist_ok=True)

        # Initialize scalers and encoders
        self.traffic_scaler = StandardScaler()
        self.compatibility_scaler = StandardScaler()
        self.route_scaler = StandardScaler()

        # Initialize models
        self.traffic_model = RandomForestRegressor(n_estimators=100, random_state=42)
        self.compatibility_model = GradientBoostingClassifier(n_estimators=100, random_state=42)
        self.route_optimizer = RandomForestRegressor(n_estimators=100, random_state=42)

        logger.info("Model trainer initialized")

    def generate_traffic_data(self, n_samples: int = 10000) -> pd.DataFrame:
        """Generate synthetic data for traffic prediction model"""
        logger.info(f"Generating {n_samples} traffic data samples")

        # Generate feature data
        data = {
            'distance_km': np.random.uniform(1, 30, n_samples),
            'hour_of_day': np.random.randint(0, 24, n_samples),
            'day_of_week': np.random.randint(0, 7, n_samples),
            'temperature': np.random.uniform(5, 40, n_samples),
            'precipitation': np.random.exponential(1, n_samples),
            'is_holiday': np.random.choice([0, 1], n_samples, p=[0.9, 0.1]),
            'traffic_level': np.random.choice(['low', 'medium', 'high', 'severe'], n_samples)
        }

        # Convert to DataFrame
        df = pd.DataFrame(data)

        # Engineer additional features
        df['is_weekend'] = (df['day_of_week'] >= 5).astype(int)
        df['is_peak_hour'] = (
            ((df['hour_of_day'] >= 7) & (df['hour_of_day'] <= 9)) |
            ((df['hour_of_day'] >= 17) & (df['hour_of_day'] <= 19))
        ).astype(int)
        df['is_night'] = ((df['hour_of_day'] >= 22) | (df['hour_of_day'] <= 5)).astype(int)
        df['is_raining'] = (df['precipitation'] > 0).astype(int)

        # Encode categorical variables
        traffic_encoder = LabelEncoder()
        df['traffic_encoded'] = traffic_encoder.fit_transform(df['traffic_level'])

        # Generate target variables based on features with some noise
        # Base time: 2 minutes per km
        df['base_time_minutes'] = df['distance_km'] * 2

        # Traffic level multiplier
        traffic_multipliers = {'low': 1.0, 'medium': 1.3, 'high': 1.7, 'severe': 2.5}
        df['traffic_multiplier'] = df['traffic_level'].map(traffic_multipliers)

        # Time adjustments
        df['peak_hour_factor'] = 1 + (df['is_peak_hour'] * 0.3)
        df['weekend_factor'] = 1 - (df['is_weekend'] * 0.1)
        df['rain_factor'] = 1 + (df['is_raining'] * 0.2)
        df['night_factor'] = 1 + (df['is_night'] * 0.1)

        # Calculate travel time with some random noise
        df['travel_time_minutes'] = (
            df['base_time_minutes'] *
            df['traffic_multiplier'] *
            df['peak_hour_factor'] *
            df['weekend_factor'] *
            df['rain_factor'] *
            df['night_factor'] *
            np.random.normal(1, 0.1, n_samples)  # Add 10% random noise
        )

        logger.info(f"Generated traffic data with shape {df.shape}")
        return df

    def generate_compatibility_data(self, n_samples: int = 5000) -> pd.DataFrame:
        """Generate synthetic data for rider-driver compatibility model"""
        logger.info(f"Generating {n_samples} compatibility data samples")

        # Generate feature data
        data = {
            'driver_rating': np.random.uniform(3.0, 5.0, n_samples),
            'rider_rating': np.random.uniform(3.0, 5.0, n_samples),
            'age_difference': np.random.randint(0, 40, n_samples),
            'same_gender': np.random.choice([0, 1], n_samples),
            'same_language': np.random.choice([0, 1], n_samples),
            'same_music_preference': np.random.choice([0, 1], n_samples),
            'smoking_preference_match': np.random.choice([0, 1], n_samples),
            'chattiness_difference': np.random.randint(0, 5, n_samples),
            'previous_rides_together': np.random.poisson(0.5, n_samples)
        }

        # Convert to DataFrame
        df = pd.DataFrame(data)

        # Generate compatibility score (target) based on features
        # Higher ratings, more previous rides, and matching preferences lead to higher compatibility
        compatibility_score = (
            0.2 * (df['driver_rating'] + df['rider_rating']) / 2 +  # Average rating (0-1 scale)
            0.15 * (1 - df['age_difference'] / 40) +                # Age similarity (0-1 scale)
            0.1 * df['same_gender'] +                              # Gender match
            0.1 * df['same_language'] +                            # Language match
            0.1 * df['same_music_preference'] +                    # Music preference match
            0.15 * df['smoking_preference_match'] +                # Smoking preference match
            0.1 * (1 - df['chattiness_difference'] / 5) +          # Chattiness similarity (0-1 scale)
            0.1 * np.minimum(df['previous_rides_together'] / 5, 1)  # Previous rides together (capped at 1)
        )

        # Add some random noise
        compatibility_score = compatibility_score + np.random.normal(0, 0.1, n_samples)

        # Clip to 0-1 range
        compatibility_score = np.clip(compatibility_score, 0, 1)

        # Classify as compatible (1) or not compatible (0) using threshold
        df['is_compatible'] = (compatibility_score >= 0.6).astype(int)

        # Store raw score for analysis
        df['compatibility_score'] = compatibility_score

        logger.info(f"Generated compatibility data with shape {df.shape}")
        return df

    def generate_route_optimization_data(self, n_samples: int = 3000) -> pd.DataFrame:
        """Generate synthetic data for route optimization model"""
        logger.info(f"Generating {n_samples} route optimization data samples")

        # Generate feature data
        data = {
            'num_passengers': np.random.randint(1, 5, n_samples),
            'total_distance_km': np.random.uniform(5, 50, n_samples),
            'total_stops': np.random.randint(2, 10, n_samples),
            'traffic_density': np.random.uniform(0.5, 2.0, n_samples),
            'time_constraints': np.random.uniform(0, 1, n_samples),  # 0 = flexible, 1 = strict
            'route_complexity': np.random.uniform(0, 1, n_samples),  # 0 = simple, 1 = complex
            'is_rush_hour': np.random.choice([0, 1], n_samples),
            'weather_severity': np.random.uniform(0, 1, n_samples)   # 0 = clear, 1 = severe
        }

        # Convert to DataFrame
        df = pd.DataFrame(data)

        # Calculate efficiency score (target)
        # Higher efficiency means better route optimization
        # More passengers, fewer stops, lower traffic = higher efficiency
        passenger_factor = 0.5 + (df['num_passengers'] / 4) * 0.5  # More passengers = more efficient
        stops_factor = 1 - (df['total_stops'] / 10) * 0.4  # Fewer stops = more efficient
        traffic_factor = 1 - (df['traffic_density'] - 0.5) * 0.5  # Less traffic = more efficient
        constraint_factor = 1 - df['time_constraints'] * 0.3  # Fewer constraints = more efficient
        complexity_factor = 1 - df['route_complexity'] * 0.4  # Less complex = more efficient
        rush_hour_factor = 1 - df['is_rush_hour'] * 0.2  # Not rush hour = more efficient
        weather_factor = 1 - df['weather_severity'] * 0.3  # Better weather = more efficient

        # Calculate base efficiency
        base_efficiency = (
            passenger_factor * 0.25 +
            stops_factor * 0.15 +
            traffic_factor * 0.2 +
            constraint_factor * 0.1 +
            complexity_factor * 0.1 +
            rush_hour_factor * 0.1 +
            weather_factor * 0.1
        )

        # Add some random noise
        efficiency_score = base_efficiency + np.random.normal(0, 0.1, n_samples)

        # Clip to 0-1 range and set as target
        df['route_efficiency'] = np.clip(efficiency_score, 0, 1)

        # Calculate time savings (minutes)
        direct_time = df['total_distance_km'] * 2  # 2 minutes per km direct route
        actual_time = direct_time * (2 - df['route_efficiency'])  # Efficiency reduces time
        df['time_savings'] = direct_time - actual_time

        logger.info(f"Generated route optimization data with shape {df.shape}")
        return df

    def train_traffic_model(self, df: pd.DataFrame) -> None:
        """Train traffic prediction model"""
        logger.info("Training traffic prediction model")

        # Select features and target
        features = [
            'distance_km', 'hour_of_day', 'day_of_week', 'is_weekend',
            'is_peak_hour', 'is_night', 'temperature', 'precipitation',
            'is_raining', 'is_holiday', 'traffic_encoded'
        ]

        X = df[features].values
        y = df['travel_time_minutes'].values

        # Scale features
        X_scaled = self.traffic_scaler.fit_transform(X)

        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            X_scaled, y, test_size=0.2, random_state=42
        )

        # Train model
        self.traffic_model.fit(X_train, y_train)

        # Evaluate model
        y_pred = self.traffic_model.predict(X_test)
        mse = mean_squared_error(y_test, y_pred)
        rmse = np.sqrt(mse)

        logger.info(f"Traffic model RMSE: {rmse:.2f} minutes")

        # Feature importance
        feature_importance = self.traffic_model.feature_importances_
        feature_names = features

        # Log feature importance instead of plotting
        logger.info("Traffic Model Feature Importance:")
        sorted_idx = np.argsort(feature_importance)[::-1]  # Sort descending
        for i in sorted_idx[:5]:  # Top 5 features
            logger.info(f"  {feature_names[i]}: {feature_importance[i]:.4f}")

        # Save model and scaler
        joblib.dump(self.traffic_model, f"{self.models_dir}/traffic_prediction_model.pkl")
        joblib.dump(self.traffic_scaler, f"{self.models_dir}/traffic_scaler.pkl")

        logger.info("Traffic prediction model trained and saved")

    def train_compatibility_model(self, df: pd.DataFrame) -> None:
        """Train rider-driver compatibility model"""
        logger.info("Training compatibility model")

        # Select features and target
        features = [
            'driver_rating', 'rider_rating', 'age_difference', 'same_gender',
            'same_language', 'same_music_preference', 'smoking_preference_match',
            'chattiness_difference', 'previous_rides_together'
        ]

        X = df[features].values
        y = df['is_compatible'].values

        # Scale features
        X_scaled = self.compatibility_scaler.fit_transform(X)

        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            X_scaled, y, test_size=0.2, random_state=42
        )

        # Train model
        self.compatibility_model.fit(X_train, y_train)

        # Evaluate model
        y_pred = self.compatibility_model.predict(X_test)
        accuracy = accuracy_score(y_test, y_pred)

        logger.info(f"Compatibility model accuracy: {accuracy:.2f}")

        # Feature importance
        feature_importance = self.compatibility_model.feature_importances_
        feature_names = features

        # Log feature importance instead of plotting
        logger.info("Compatibility Model Feature Importance:")
        sorted_idx = np.argsort(feature_importance)[::-1]  # Sort descending
        for i in sorted_idx[:5]:  # Top 5 features
            logger.info(f"  {feature_names[i]}: {feature_importance[i]:.4f}")

        # Save model and scaler
        joblib.dump(self.compatibility_model, f"{self.models_dir}/compatibility_model.pkl")
        joblib.dump(self.compatibility_scaler, f"{self.models_dir}/compatibility_scaler.pkl")

        logger.info("Compatibility model trained and saved")

    def train_route_optimizer(self, df: pd.DataFrame) -> None:
        """Train route optimization model"""
        logger.info("Training route optimization model")

        # Select features and target
        features = [
            'num_passengers', 'total_distance_km', 'total_stops', 'traffic_density',
            'time_constraints', 'route_complexity', 'is_rush_hour', 'weather_severity'
        ]

        X = df[features].values
        y = df['route_efficiency'].values

        # Scale features
        X_scaled = self.route_scaler.fit_transform(X)

        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            X_scaled, y, test_size=0.2, random_state=42
        )

        # Train model
        self.route_optimizer.fit(X_train, y_train)

        # Evaluate model
        y_pred = self.route_optimizer.predict(X_test)
        mse = mean_squared_error(y_test, y_pred)
        rmse = np.sqrt(mse)

        logger.info(f"Route optimizer RMSE: {rmse:.4f}")

        # Feature importance
        feature_importance = self.route_optimizer.feature_importances_
        feature_names = features

        # Log feature importance instead of plotting
        logger.info("Route Optimizer Feature Importance:")
        sorted_idx = np.argsort(feature_importance)[::-1]  # Sort descending
        for i in sorted_idx[:5]:  # Top 5 features
            logger.info(f"  {feature_names[i]}: {feature_importance[i]:.4f}")

        # Save model and scaler
        joblib.dump(self.route_optimizer, f"{self.models_dir}/route_optimizer_model.pkl")
        joblib.dump(self.route_scaler, f"{self.models_dir}/route_scaler.pkl")

        logger.info("Route optimizer model trained and saved")

    def train_all_models(self) -> None:
        """Generate data and train all ride matching models"""
        # Traffic prediction model
        traffic_data = self.generate_traffic_data()
        self.train_traffic_model(traffic_data)

        # Compatibility model
        compatibility_data = self.generate_compatibility_data()
        self.train_compatibility_model(compatibility_data)

        # Route optimization model
        route_data = self.generate_route_optimization_data()
        self.train_route_optimizer(route_data)

        logger.info("All ride matching models trained successfully")

def main():
    """Main function to train models"""
    try:
        trainer = RideMatchingModelTrainer(models_dir='models')
        trainer.train_all_models()
        print("✅ Ride matching models trained successfully!")
    except Exception as e:
        logger.error(f"Error training models: {e}", exc_info=True)
        print(f"❌ Error training models: {e}")

if __name__ == "__main__":
    main()